package consts

// GJK标签类型常量
const (
	GJKTagMust   = 1 // 必看
	GJKTagSelect = 2 // 选看
	GJKTagNo     = 3 // 不看
)

// GJK标签映射
var GJKTagMap = map[int]string{
	GJKTagMust:   "必看",
	GJKTagSelect: "选看",
	GJKTagNo:     "不看",
}

// GJK标签选项结构
type GJKTagOption struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}

// GetGJKTagOptions 获取GJK标签选项列表
func GetGJKTagOptions() []GJKTagOption {
	return []GJKTagOption{
		{Label: GJKTagMap[GJKTagMust], Value: GJKTagMust},
		{Label: GJKTagMap[GJKTagSelect], Value: GJKTagSelect},
		{Label: GJKTagMap[GJKTagNo], Value: GJKTagNo},
	}
}