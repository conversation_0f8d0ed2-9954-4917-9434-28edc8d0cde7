package consts

// IMC课程类型枚举
const (
	NewCourseTypeImcZero = 48 // imc体验课
	NewCourseTypeImcLx   = 49 // imc专题课
	NewCourseTypeImcBzr  = 50 // imc班课
)

// IMC业务新课程类型枚举集合
var NewCourseTypeImcMap = []int{
	NewCourseTypeImcZero,
	NewCourseTypeImcLx,
	NewCourseTypeImcBzr,
}

const (
	AllowAutoCall    = 1
	NotAllowAutoCall = 0
)

/*注意：字段解释
 * 此字段代表子系统MUSE中的辅导业务线
 *因source、type等字段较常用易于其他调用混淆
 *产生歧义，故使用“生产线”逻辑以line做定义
 */
const LINE_FUDAO = 1

const LINE_LPC = 2

const BUSINESS_TYPE_FOR_PUBLIC_SEA = "publicSea"

const (
	CourseServiceTypeLX    = "LX"
	CourseServiceTypeDXB   = "ZJ"
	CourseServiceTypeOther = "OTHER"
)

var MapCourseServiceTypeName = map[string]string{
	CourseServiceTypeLX:    "拉新课",
	CourseServiceTypeDXB:   "正价课",
	CourseServiceTypeOther: "其他课程",
}

// 课程服务类型字符串到int的映射，用于API响应
var MapCourseServiceTypeToInt = map[string]int{
	CourseServiceTypeLX:    1, // 督学服务
	CourseServiceTypeDXB:   2, // 班主任服务
	CourseServiceTypeOther: 3, // 其他课程
}

// 学员详情页配置相关的课程性质常量
// 辅导的课程性质
var FdCoursePriceTag = []int{99, 101, 102, 103, 104, 105, 107, 108, 114, 115, 116, 117, 122, 123, 124, 126, 130, 131, 132, 133, 134, 127, 128, 129, 136, 137, 138, 139, 142, 146, 147, 148, 149, 150, 151, 152, 153, 154}

// LPC的课程性质
var LpcCoursePriceTag = []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 110, 111, 112, 113, 118, 119, 120, 121, 140, 141, 143, 144}

// 小学X课程性质 
var LpXiaoxueXCoursePriceTag = []int{21, 24, 119, 120, 121}

// 辅导小学规划课程性质
var FdXxgjCoursePriceTag = []int{146, 147, 148}

// 辅导小学低段拓科
var FdLittleKidExtentSubject = []int{149}
