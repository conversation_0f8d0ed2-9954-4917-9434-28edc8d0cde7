package consts

const (
	STATUS_BIND    = 0
	STATUS_UNTYING = 1
)

type StudentExportStatus int

const (
	StudentExportStatusPending    StudentExportStatus = 0
	StudentExportStatusInProgress StudentExportStatus = 1
	StudentExportStatusFinished   StudentExportStatus = 2
	StudentExportStatusDownloaded StudentExportStatus = 3
	StudentExportStatusFailed     StudentExportStatus = 4
	StudentExportStatusCanceled   StudentExportStatus = 5
)

const (
	ExportStudentTableHeadLesson = "章节信息:"
	ExportStudentTableHeadTask   = "任务信息:"
)

const BatchSize = 500
const TowerAPIBatchSize = 100
const BigBatchSize = 2000

const (
	ARK_MODULE_TARGET              = 1    //任务指标
	ARK_MODULE_OVERVIEW            = 2    //任务概览
	ARK_MODULE_FEATURES            = 3    //功能工具
	ARK_MODULE_FIELDS              = 4    //学生列表字段列
	ARK_MODULE_MULTI_FIELDS        = 5    //学生列表字段列 lpc
	ARK_MODULE_DEER_PROGRAM_FIELDS = 10   //学生列表字段列 小鹿编程
	ARK_MODULE_NO_COURSE_FIELDS    = 20   //无课例子
	ARK_MODULE_DATA_PANEL          = 100  //数据面板 (替代任务指标、任务概览)
	ARK_MODULE_TARGET_DB_SOURCE    = 1000 //指标概览数据源
	ARK_MODULE_FIELDS_API_SOURCE   = 1001 //字段列数据源
	ARK_MODULE_EXPORT_DB_SOURCE    = 1002 //导出数据源
)

var ArkModuleMapBusinessLine = map[int]int{
	ARK_MODULE_TARGET:              BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_OVERVIEW:            BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_FEATURES:            BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_FIELDS:              BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_MULTI_FIELDS:        BUSINESS_LINE_LPC,
	ARK_MODULE_DEER_PROGRAM_FIELDS: BUSINESS_LINE_DEER_PROGRAM,
	ARK_MODULE_NO_COURSE_FIELDS:    BUSINESS_LINE_NO_COURSE,
	ARK_MODULE_DATA_PANEL:          BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_TARGET_DB_SOURCE:    BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_FIELDS_API_SOURCE:   BUSINESS_LINE_ASSISTANT,
	ARK_MODULE_EXPORT_DB_SOURCE:    BUSINESS_LINE_ASSISTANT,
}

const BUSINESS_LINE_LPC = 1          //LPC
const BUSINESS_LINE_ASSISTANT = 2    //辅导班课
const BUSINESS_LINE_DEER_PROGRAM = 3 //小鹿编程
const BUSINESS_LINE_NO_COURSE = 4    //无课例子列表

const (
	ArkRuleConfigExportFieldMap      = "ExportFieldMap"
	ArkRuleConfigFilterMap           = "FilterMap"
	ArkRuleConfigServiceConfig       = "ServiceConfig"
	ArkRuleConfigFilterMapMulti      = "FilterMapMulti"
	ArkRuleConfigDataSourceInfo      = "DataSourceInfo"
	ArkRuleConFigFieldsFuncMap       = "FieldsFuncMap"
	ArkRuleConFigFieldsOptions       = "Options"
	ArkRuleConFigFilterFieldsFuncMap = "FilterFieldsFuncMap"
	ArkRuleConFigDbQuery             = "DbQuery"

	ArkRuleConFigMainKeyKey          = "Key"
	ArkRuleConFigMainKeyTaskKey      = "TaskKey"
	ArkRuleConFigMainKeyComponentKey = "ComponentKey"
)

const (
	ARK_DB_SOURCE_TYPE_RAL = 1
)

var SourceTypeMap = map[int64]string{
	ARK_DB_SOURCE_TYPE_RAL: "接口数据源",
}

const FILTER_LESSON_SELECT_KEY = "lesson_list_filter_all_lesson_key"

const CONFIG_SOURCE_TYPE_LESSON = 0
const CONFIG_SOURCE_TYPE_ARK = 1
const CONFIG_SOURCE_TYPE_CUSTOM = 2

type ConfigTypelabel struct {
	Name string
	Key  string
}

const (
	SERVICE_TYPE_COURSE  = 0  //课程筛选维度
	SERVICE_TYPE_LESSON  = 1  //章节筛选维度
	SERVICE_TYPE_GENERAL = 10 //通用筛选维度
)

var ServiceTypeList = []int{
	SERVICE_TYPE_COURSE,
	SERVICE_TYPE_LESSON,
	SERVICE_TYPE_GENERAL,
}

var ServiceTypeMap = map[int]ConfigTypelabel{
	SERVICE_TYPE_COURSE:  {Name: "课程信息", Key: "course"},
	SERVICE_TYPE_LESSON:  {Name: "章节信息", Key: "lesson"},
	SERVICE_TYPE_GENERAL: {Name: "通用筛选", Key: "general"},
}

var ConfigDimensionList = []int{
	CONFIG_SOURCE_TYPE_LESSON,
	CONFIG_SOURCE_TYPE_ARK,
	CONFIG_SOURCE_TYPE_CUSTOM,
}

var ConfigDimensionMap = map[int]ConfigTypelabel{
	CONFIG_SOURCE_TYPE_LESSON: {Name: "章节维度", Key: "lesson"},
	CONFIG_SOURCE_TYPE_ARK:    {Name: "学员维度", Key: "ark"},
	CONFIG_SOURCE_TYPE_CUSTOM: {Name: "自定义", Key: "custom"},
}

const (
	ARK_SOURCE_DIMENSION_LEADS   = 1
	ARK_SOURCE_DIMENSION_STUDENT = 2
)

const (
	BaseArkTemplateOrderAssistantUid = 9999999999 // 运营的环节顺序默认 uid
)

const (
	RWLBYHGrayKey = "new_ark_gray"
)

const (
	TargetGroup    = "collection"
	OverviewGroup  = "overview"
	DataPanelGroup = "dataPanel"
)

const ARK_APP_TASK_LIST = 1           //任务列表
const ARK_APP_COURSE_DATA = 2         //班级数据
const ARK_APP_DETAIL_TAG = 3          //维系详情标签
const ARK_APP_DETAIL_CARD = 4         //维系详情课程卡片
const ARK_APP_DETAIL_COLUMN = 5       //维系详情字段列
const ARK_APP_DETAIL_DELAMINATION = 6 //维系详情分层
