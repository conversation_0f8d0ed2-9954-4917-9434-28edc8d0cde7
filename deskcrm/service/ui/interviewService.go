package ui

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dal"
	"deskcrm/api/duxuesc"
	"deskcrm/api/examcore"
	"deskcrm/api/mesh"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/models"
	"deskcrm/service/innerapi"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type interviewSerivce struct{}

var (
	InterviewService interviewSerivce
)

// InterviewReferLpc 获取lpc侧家访参考信息
// 对应PHP的 InterviewReferLpc.php 接口逻辑
func (s interviewSerivce) InterviewReferLpc(ctx *gin.Context, param *inputStudent.InterviewReferLpcParam) (rsp outputStudent.InterviewReferLpcOutput, err error) {
	rsp = outputStudent.InterviewReferLpcOutput{
		HasLpcCourse:     0,
		HasQuestionnaire: 0,
		HasRegistTest:    0,
		LpcRemarks:       []outputStudent.ReferRemarkInfo{},
	}

	// 1. 获取班课学员从LPC侧转化课程信息
	referCourse, err := s.getReferCourse(ctx, param.CourseId, param.StudentUid)
	// 如果没有转化课程信息，直接返回
	if err != nil || referCourse == nil {
		return rsp, err
	}
	rsp.HasLpcCourse = 1

	// 2. 获取课程基础信息
	courseInfo, err := dal.GetCourseBaseByCourseIds(ctx, []int64{referCourse.CourseId}, []string{"courseName", "cpuId", "mainGradeId"})
	if err != nil {
		return rsp, err
	}

	var courseName string
	var cpuId int64
	var mainGradeId int64
	if info, exists := courseInfo[referCourse.CourseId]; exists {
		courseName = info.CourseName
		cpuId = info.CpuId
		mainGradeId = info.MainGradeId
	}

	// 3. 获取挖需问卷信息
	waxuQuestionnaire, err := s.getWaxuQuestionnaire(ctx, referCourse.CourseId, param.StudentUid)
	if err != nil {
		return rsp, err
	}
	waxuQuestionnaire.CourseName = courseName
	rsp.Questionnaire = &waxuQuestionnaire
	// 设置hasQuestionnaire标识，对应PHP版本的逻辑
	if waxuQuestionnaire.IsBind == 1 {
		rsp.HasQuestionnaire = 1
	}

	// 4. 获取摸底测信息（使用lpcCourseId，对应PHP版本的逻辑）
	registTest, err := s.getRegistTest(ctx, referCourse.CourseId, param.StudentUid, courseName, cpuId, mainGradeId)
	if err != nil {
		return rsp, err
	}
	rsp.HasRegistTest = 1
	rsp.RegistTest = &registTest

	// 5. 获取备注信息
	referRemarks, err := s.getReferRemarks(ctx, referCourse.CourseId, referCourse.LpcUid, param.StudentUid, courseName)
	if err != nil {
		return rsp, err
	}
	rsp.LpcRemarks = referRemarks

	return rsp, nil
}

// getReferCourse 获取班课学员从LPC侧转化课程
// 对应PHP的 AssistantDesk_InterviewReferLpc::referCourse 方法
func (s interviewSerivce) getReferCourse(ctx *gin.Context, courseId, studentUid int64) (*outputStudent.ReferCourseInfo, error) {
	if courseId <= 0 || studentUid <= 0 {
		zlog.Warnf(ctx, "getReferCourse invalid params: courseId=%d, studentUid=%d", courseId, studentUid)
		return nil, nil
	}

	// 调用duxuesc服务获取LPC课程信息
	client := duxuesc.NewClient()
	lpcData, err := client.GetLpcUidCourseId(ctx, courseId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "getReferCourse GetLpcUidCourseId failed: %v", err)
		return nil, err
	}

	if lpcData == nil || len(*lpcData) == 0 {
		return nil, nil
	}

	// 取第一个结果
	firstResult := (*lpcData)[0]
	if firstResult.CourseId <= 0 {
		return nil, nil
	}

	return &outputStudent.ReferCourseInfo{
		CourseId: firstResult.CourseId,
		LpcUid:   firstResult.LpcUid,
	}, nil
}

// getWaxuQuestionnaire 获取挖需问卷信息
// 对应PHP的 AssistantDesk_InterviewReferLpc::waxuQuestionnaire 方法
func (s interviewSerivce) getWaxuQuestionnaire(ctx *gin.Context, lpcCourseId, studentUid int64) (result outputStudent.QuestionnaireInfo, err error) {
	if lpcCourseId <= 0 || studentUid <= 0 {
		zlog.Warnf(ctx, "getWaxuQuestionnaire invalid params: lpcCourseId=%d, studentUid=%d", lpcCourseId, studentUid)
		return result, nil
	}

	// 调用duxuesc服务获取挖需问卷信息
	client := duxuesc.NewClient()
	waxuData, err := client.GetSurveyShowFields(ctx, lpcCourseId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "getWaxuQuestionnaire GetSurveyShowFields failed: %v", err)
		return result, nil
	}

	if waxuData == nil {
		return result, nil
	}

	// 格式化完成时间
	var finishTime string
	if waxuData.CreateTime > 0 {
		finishTime = time.Unix(waxuData.CreateTime, 0).Format("2006-01-02 15:04:05")
	}

	// 处理标签
	labels := waxuData.SurveyShowFields
	if labels == nil {
		labels = []string{}
	}

	return outputStudent.QuestionnaireInfo{
		IsFinish:   waxuData.Status,
		Labels:     labels,
		DetailUrl:  waxuData.NeedSurveyUrl,
		FinishTime: finishTime,
		IsBind:     waxuData.IsBind,
	}, nil
}

// getRegistTest 获取摸底测信息
func (s interviewSerivce) getRegistTest(ctx *gin.Context, lpcCourseId, studentUid int64, courseName string, cpuId, mainGradeId int64) (result outputStudent.RegistTestInfo, err error) {
	if cpuId <= 0 {
		zlog.Warnf(ctx, "getRegistTest 课程cpuId信息获取失败: cpuId=%d", cpuId)
		return result, nil
	}

	// 1. 获取CPU绑定的摸底测信息
	cpuBindExams, err := innerapi.ExamService.GetCpuBindExams(ctx, []int64{cpuId}, examcore.ExamTypeSurvey)
	if err != nil {
		zlog.Warnf(ctx, "getRegistTest GetCpuBindExams failed: cpuId=%d, err=%v", cpuId, err)
		return result, err
	}

	// 2. 从绑定信息中提取examId
	var examId int64 = 0
	if bindExams, exists := cpuBindExams[cpuId]; exists && len(bindExams) > 0 {
		// 取第一个examId
		for examIdInt := range bindExams {
			examId = int64(examIdInt)
			break
		}
	}

	// 3. 检查examId是否存在
	if examId <= 0 {
		zlog.Warnf(ctx, "getRegistTest 课程examId信息获取失败: cpuId=%d", cpuId)
		return result, nil
	}

	// 4. 获取摸底测详情
	registTestInfo, err := innerapi.ExamService.RegistTest(ctx, examId, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "getRegistTest RegistTest failed: examId=%d, studentUid=%d, err=%v", examId, studentUid, err)
		return result, err
	}

	if registTestInfo == nil {
		return result, nil
	}

	// 5. 构造返回结构，添加额外字段
	result = outputStudent.RegistTestInfo{
		IsFinish:   registTestInfo.IsFinish,
		CostTime:   registTestInfo.CostTime,
		Score:      registTestInfo.Score,
		FinishTime: registTestInfo.FinishTime,
		CourseId:   lpcCourseId,
		CourseName: courseName,
		StudentUid: studentUid,
		Department: consts.GetDepartmentIdByGradeId(mainGradeId),
	}

	return result, nil
}

// getReferRemarks 获取备注信息
func (s interviewSerivce) getReferRemarks(ctx *gin.Context, lpcCourseId, lpcUid, studentUid int64, courseName string) ([]outputStudent.ReferRemarkInfo, error) {
	if lpcCourseId <= 0 || lpcUid <= 0 || studentUid <= 0 {
		zlog.Warnf(ctx, "getReferRemarks invalid params: lpcCourseId=%d, lpcUid=%d, studentUid=%d", lpcCourseId, lpcUid, studentUid)
		return []outputStudent.ReferRemarkInfo{}, nil
	}

	// 调用duxuesc服务获取备注信息
	client := duxuesc.NewClient()
	remarks, err := client.GetLpcCourseRemark(ctx, lpcCourseId, lpcUid, studentUid)
	if err != nil {
		zlog.Warnf(ctx, "getReferRemarks GetLpcCourseRemark failed: %v", err)
		return nil, err
	}

	if len(remarks) == 0 {
		return []outputStudent.ReferRemarkInfo{}, nil
	}

	// 格式化备注信息
	var formatRemarks []outputStudent.ReferRemarkInfo
	for _, remark := range remarks {
		var remarkTime string
		if remark.Time > 0 {
			remarkTime = time.Unix(remark.Time, 0).Format("2006-01-02 15:04:05")
		}

		formatRemarks = append(formatRemarks, outputStudent.ReferRemarkInfo{
			RemarkTime: remarkTime,
			Remark:     remark.Remark,
			CourseName: courseName,
		})
	}

	return formatRemarks, nil
}

// GetInterviewRecord 获取访谈记录
func (s interviewSerivce) GetInterviewRecord(ctx *gin.Context, param *inputStudent.InterviewRecordV2Param) (rsp outputStudent.InterviewRecordOutput, err error) {
	oldRecord, err := s.getManualInterviewRecords(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "getInterviewRecordV2 getManualInterviewRecords failed: %v", err)
		return rsp, err
	}
	rsp.InterviewRecord = oldRecord
	rsp.SaleCallRecord = []any{}
	return rsp, nil
}

// GetInterviewRecordV2 获取访谈记录V2
func (s interviewSerivce) GetInterviewRecordV2(ctx *gin.Context, param *inputStudent.InterviewRecordV2Param) (rsp outputStudent.InterviewRecordV2Output, err error) {
	rsp = outputStudent.InterviewRecordV2Output{
		Page:     param.Page,
		PageSize: param.PageSize,
	}

	// 1. 获取手动维系记录
	oldRecord, err := s.getManualInterviewRecords(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "getInterviewRecordV2 getManualInterviewRecords failed: %v", err)
		return rsp, err
	}

	// 2. 获取AI维系记录
	aiRecordData, err := s.getAIInterviewRecords(ctx, param)
	if err != nil {
		zlog.Warnf(ctx, "getInterviewRecordV2 getAIInterviewRecords failed: %v", err)
		return rsp, err
	}

	// 3. 合并记录
	mergedArray := s.mergeInterviewRecords(oldRecord, aiRecordData, param.InterviewType)

	// 4. 分页
	start := (param.Page - 1) * param.PageSize
	end := min(start + param.PageSize, len(mergedArray))

	if start < len(mergedArray) {
		rsp.InterviewRecord = mergedArray[start:end]
	} else {
		rsp.InterviewRecord = []outputStudent.MergedRecord{}
	}

	// 5. 设置总数
	rsp.Total = aiRecordData.Total + len(oldRecord)

	return rsp, nil
}

// getManualInterviewRecords 获取手动维系记录
// 对应PHP的 Service_Page_Desk_Student_InterviewRecord::execute 方法
func (s interviewSerivce) getManualInterviewRecords(ctx *gin.Context, param *inputStudent.InterviewRecordV2Param) ([]outputStudent.InterviewRecordItem, error) {
	// 构造查询条件
	studentUid := param.StudentUid
	assistantUid := param.AssistantUid
	courseId := param.CourseId
	recordType := param.Type

	// 参数校验
	if studentUid <= 0 || assistantUid <= 0 || recordType <= 0 {
		zlog.Warnf(ctx, "getManualInterviewRecords invalid params: studentUid=%d, assistantUid=%d, recordType=%d", studentUid, assistantUid, recordType)
		return []outputStudent.InterviewRecordItem{}, nil
	}

	// 计算开始时间（180天前）
	beginTime := time.Now().AddDate(0, 0, -180).Unix()

	// 查询访谈记录
	interviews, err := models.TblStudentInterviewDao.GetByUidAndCreateTime(ctx, studentUid, false, beginTime)
	if err != nil {
		zlog.Warnf(ctx, "getManualInterviewRecords GetByUidAndCreateTime failed: %v", err)
		return []outputStudent.InterviewRecordItem{}, nil
	}

	// 获取课程信息
	courseIds := make([]int64, 0)
	for _, interview := range interviews {
		courseIds = append(courseIds, int64(interview.CourseID))
	}

	var courseMap map[int64]dal.CourseInfo
	if len(courseIds) > 0 {
		courseMap, err = dal.GetCourseBaseByCourseIds(ctx, courseIds, []string{"courseId", "courseName"})
		if err != nil {
			zlog.Warnf(ctx, "getManualInterviewRecords GetCourseBaseByCourseIds failed: %v", err)
		}
	} else {
		courseMap = make(map[int64]dal.CourseInfo)
	}

	// 获取辅导老师uid对应的真人信息
	assistantUids := make([]int64, 0)
	for _, interview := range interviews {
		assistantUids = append(assistantUids, interview.OperatorUID)
	}
	// 去重
	assistantUidsMap := make(map[int64]bool)
	uniqueAssistantUids := make([]int64, 0)
	for _, uid := range assistantUids {
		if !assistantUidsMap[uid] {
			assistantUidsMap[uid] = true
			uniqueAssistantUids = append(uniqueAssistantUids, uid)
		}
	}

	var personInfos map[string]mesh.PersonInfo
	if len(uniqueAssistantUids) > 0 {
		meshClient := mesh.NewClient()
		personInfos, err = meshClient.GetPersonInfosByAssistantUids(ctx, uniqueAssistantUids)
		if err != nil {
			zlog.Warnf(ctx, "getManualInterviewRecords GetPersonInfosByAssistantUids failed: %v", err)
			personInfos = make(map[string]mesh.PersonInfo)
		}
	} else {
		personInfos = make(map[string]mesh.PersonInfo)
	}

	// 格式化记录
	interviewRecords := make([]outputStudent.InterviewRecordItem, 0)
	for _, interview := range interviews {
		// 类型过滤
		if recordType == consts.StudentInterviewViewCourse && int64(interview.CourseID) != courseId { // 只看该课程
			continue
		}
		if recordType == consts.StudentInterviewViewSelf && interview.OperatorUID != assistantUid { // 只看自己
			continue
		}

		// 获取课程名称
		courseName := ""
		if courseInfo, exists := courseMap[int64(interview.CourseID)]; exists {
			courseName = courseInfo.CourseName
		}

		// 解析扩展数据
		var extData map[string]interface{}
		if interview.ExtData != "" {
			json.Unmarshal([]byte(interview.ExtData), &extData)
		}

		// 获取最后内容
		var lastContent interface{}
		if extData != nil {
			lastContent = extData["lastContent"]
		}

		// 获取真人老师姓名并格式化interviewer字段
		var interviewer string
		if interview.Type == consts.StudentInterviewTftcJsqValue { // 对应PHP的TFTC_JSQ_VALUE = 128
			interviewer = "投放销售"
		} else {
			// 获取真人姓名
			personName := "账号已解绑"
			assistantUidStr := fmt.Sprintf("%d", interview.OperatorUID)
			if personInfo, exists := personInfos[assistantUidStr]; exists && personInfo.StaffName != "" {
				personName = personInfo.StaffName
			}
			interviewer = fmt.Sprintf("%s(%s)", interview.Operator, personName)
		}

		// 构造访谈记录项
		record := outputStudent.InterviewRecordItem{
			ID:            interview.ID,
			InterviewTime: time.Unix(interview.InterviewTime, 0).Format("2006-01-02 15:04:05"),
			Content:       interview.Content,
			CourseName:    courseName,
			Interviewer:   interviewer, // 使用格式化后的interviewer
			ChannelType:   interview.ChannelType,
			CanEdit:       boolToInt(assistantUid == interview.OperatorUID && lastContent == nil),
			Friendliness:  interview.Friendliness,
			RoleType:      interview.RoleType,
			PhaseId:       0,
			PhaseKey:      0,
			CreateTime:    interview.InterviewTime,
			RecordType:    consts.StudentInterviewRecordTypeManual, // 手动记录
		}

		// 转换访谈类型
		record.InterviewType = s.convertInterviewType(interview.Type)

		interviewRecords = append(interviewRecords, record)
	}

	return interviewRecords, nil
}

// getAIInterviewRecords 获取AI维系记录
// 对应PHP的 Api_Assistantdeskgo_Api::getAIRecord 方法
func (s interviewSerivce) getAIInterviewRecords(ctx *gin.Context, param *inputStudent.InterviewRecordV2Param) (*assistantdeskgo.GetAIRecordResp, error) {
	// 转换type参数
	aiCallRecordType := consts.StudentInterviewAIViewAll
	if param.Type == consts.StudentInterviewViewSelf { // 只看自己，手动维系记录 type = 3
		aiCallRecordType = consts.StudentInterviewAIViewSelf // 只看自己，ai维系记录 type = 2
	}

	if param.Type == consts.StudentInterviewViewAll { // 查看全部，手动维系记录 type = 1
		aiCallRecordType = consts.StudentInterviewAIViewAll // 查看全部，ai维系记录 type = 0
	}

	// 调用AI记录API
	client := assistantdeskgo.NewClient()
	aiRecordData, err := client.GetAIRecord(ctx, param.StudentUid, param.CourseId, aiCallRecordType, param.Page, param.PageSize)
	if err != nil {
		zlog.Warnf(ctx, "getAIInterviewRecords GetAIRecord failed: %v", err)
		return &assistantdeskgo.GetAIRecordResp{}, nil
	}

	return aiRecordData, nil
}

// mergeInterviewRecords 合并手动和AI记录
func (s interviewSerivce) mergeInterviewRecords(oldRecords []outputStudent.InterviewRecordItem, aiRecordData *assistantdeskgo.GetAIRecordResp, interviewType int) []outputStudent.MergedRecord {
	var mergedRecords []outputStudent.MergedRecord

	// 处理手动记录
	if interviewType != consts.StudentInterviewRecordTypeAI {
		for _, record := range oldRecords {
			mergedRecord := outputStudent.MergedRecord{
				RecordType:    1, // 手动记录
				ID:            &record.ID,
				InterviewTime: &record.InterviewTime,
				Content:       &record.Content,
				CourseName:    &record.CourseName,
				Interviewer:   &record.Interviewer,
				ChannelType:   &record.ChannelType,
				CanEdit:       &record.CanEdit,
				Friendliness:  &record.Friendliness,
				RoleType:      &record.RoleType,
				PhaseId:       &record.PhaseId,
				PhaseKey:      &record.PhaseKey,
				InterviewType: &record.InterviewType,
				CreateTime:    record.CreateTime,
			}
			mergedRecords = append(mergedRecords, mergedRecord)
		}
	}

	// 处理AI记录
	if interviewType != consts.StudentInterviewRecordTypeManual {
		for _, aiRecord := range aiRecordData.CallDetailList {
			// 将字符串类型的CallId转换为int64（用于ID字段）
			callIdInt64, _ := strconv.ParseInt(aiRecord.CallId, 10, 64)

			// 将复杂字段转换为any类型
			var contentArray any = aiRecord.Content
			var tags any = aiRecord.Tags

			mergedRecord := outputStudent.MergedRecord{
				RecordType:   2, // AI记录
				ID:           &callIdInt64,
				CallId:       &aiRecord.CallId,
				StartTime:    &aiRecord.StartTime,
				StopTime:     &aiRecord.StopTime,
				Status:       &aiRecord.Status,
				Type:         &aiRecord.Type,
				RecordFile:   &aiRecord.RecordFile,
				SourceType:   &aiRecord.SourceType,
				CourseName:   &aiRecord.CourseName,
				CourseId:     &aiRecord.CourseId,
				CallMode:     &aiRecord.CallMode,
				CallModeStr:  &aiRecord.CallModeStr,
				Duration:     &aiRecord.Duration,
				Abstract:     &aiRecord.Abstract,
				Accurate:     &aiRecord.Accurate,
				Inaccurate:   &aiRecord.Inaccurate,
				CurrentTime:  &aiRecord.CurrentTime,
				Version:      &aiRecord.Version,
				ContentArray: &contentArray,
				Tags:         &tags,
				CreateTime:   aiRecord.CreateTime,
			}
			mergedRecords = append(mergedRecords, mergedRecord)
		}
	}

	// 按创建时间倒序排序（对应PHP的usort逻辑）
	sort.Slice(mergedRecords, func(i, j int) bool {
		return mergedRecords[i].CreateTime > mergedRecords[j].CreateTime
	})

	return mergedRecords
}

// convertInterviewType 转换访谈类型
func (s interviewSerivce) convertInterviewType(dbType int) []int {
	interviewType := make([]int, 0)
	for bitType, interviewValue := range consts.InterviewTypeValueMap {
		if (dbType & interviewValue) > 0 {
			interviewType = append(interviewType, bitType)
		}
	}

	return interviewType
}

// boolToInt 布尔值转整数
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}
