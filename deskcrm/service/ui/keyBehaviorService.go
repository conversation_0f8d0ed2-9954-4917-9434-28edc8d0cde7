package ui

import (
	"deskcrm/api/allocate"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/api/dataproxy"
	"deskcrm/api/examcore"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"fmt"
	"math"
	"sort"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type keyBehaviorService struct{}

var (
	KeyBehaviorService keyBehaviorService
)

// KeyBehaviorData 关键行为数据结构
type KeyBehaviorData struct {
	StudentUid        int                              `json:"studentUid"`
	CourseId          int                              `json:"courseId"`
	LeadsId           int                              `json:"leadsId"`
	PersonUid         int                              `json:"personUid"` // LPC教师UID
	CourseInfo        dal.CourseInfo                   `json:"courseInfo"`
	LessonInfos       map[string]dal.LessonInfo        `json:"lessonInfos"`
	LessonCnt         int                              `json:"lessonCnt"`
	LpcLUData         map[int64]*das.StudentLessonInfo `json:"lpcLUData"`
	LeadsEsData       map[string]interface{}           `json:"leadsEsData"` // ES数据
	PreCompliTestData bool                             `json:"preCompliTestData"`
	NeedSurveyStatus  bool                             `json:"needSurveyStatus"`
	OrderStatus       bool                             `json:"orderStatus"`
	FnOrderStatus     bool                             `json:"fnOrderStatus"`
	AttendNum         int                              `json:"attendNum"`
	FinishNum         int                              `json:"finishNum"`
	PlayStatusCnt     int                              `json:"playStatusCnt"`
	PreOrderStatus    int                              `json:"preOrderStatus"`
}

// GetKeyBehavior 获取学生关键行为数据
func (s keyBehaviorService) GetKeyBehavior(ctx *gin.Context, param *inputStudent.KeyBehaviorParam) (rsp outputStudent.KeyBehaviorOutput, err error) {
	// 初始化数据结构
	data := &KeyBehaviorData{
		StudentUid: param.StudentUid,
		CourseId:   param.CourseId,
		LeadsId:    param.LeadsId,
	}

	// 如果没有提供leadsId，尝试获取
	if param.LeadsId == 0 {
		leadsId, personUid, err := s.getLeadsIdAndPersonUidByStudentUidCourseId(ctx, param.StudentUid, param.CourseId)
		if err != nil {
			zlog.Warnf(ctx, "getLeadsIdAndPersonUidByStudentUidCourseId failed, err:%v", err)
		} else {
			data.LeadsId = leadsId
			data.PersonUid = personUid
		}
	}

	// 调试日志：基础数据获取完成
	components.DebugfWithJSON(ctx, "GetKeyBehavior basic data initialized: %s", map[string]interface{}{
		"leadsId":   data.LeadsId,
		"personUid": data.PersonUid,
		"data":      data,
	})

	// 初始化各种数据
	if err = s.initCourseLessonInfos(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSONAndCount(ctx, "GetKeyBehavior initCourseLessonInfos completed, lessonCnt: %d, data: %s", data.LessonCnt, data.CourseInfo)

	if err = s.initLeadsEsData(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initLeadsEsData completed, leadsEsData: %s", data.LeadsEsData)

	if err = s.initLpcLUData(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSONAndCount(ctx, "GetKeyBehavior initLpcLUData completed, lpcDataCount: %d, lpcLUData: %s", len(data.LpcLUData), data.LpcLUData)

	if err = s.initPreCompliTestData(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initPreCompliTestData completed, preCompliTestData: %s", data.PreCompliTestData)

	if err = s.initNeedSurveyStatus(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initNeedSurveyStatus completed, needSurveyStatus: %s", data.NeedSurveyStatus)

	if err = s.initOrderStatus(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initOrderStatus completed, orderStatus: %s", data.OrderStatus)

	if err = s.initFnOrderStatus(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initFnOrderStatus completed, fnOrderStatus: %s", data.FnOrderStatus)

	if err = s.initAttendNum(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initAttendNum completed, attendNum: %s", data.AttendNum)

	if err = s.initFinishNum(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initFinishNum completed, finishNum: %s", data.FinishNum)

	if err = s.initLpcPlayStatus(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initLpcPlayStatus completed, playStatusCnt: %s", data.PlayStatusCnt)

	if err = s.initPreOrderInfos(ctx, data); err != nil {
		return
	}
	components.DebugfWithJSON(ctx, "GetKeyBehavior initPreOrderInfos completed, preOrderStatus: %s", data.PreOrderStatus)

	// 调试日志：所有数据初始化完成
	components.DebugfWithJSON(ctx, "GetKeyBehavior all data initialization completed, final data: %s", data)

	// 格式化数据
	rsp = s.formatData(ctx, data)

	// 调试日志：格式化完成
	components.DebugfWithJSONAndCount(ctx, "GetKeyBehavior formatData completed, groupCount: %d, result: %s", len(rsp.GroupList), rsp)

	zlog.Infof(ctx, "GetKeyBehavior success, studentUid:%d, courseId:%d, leadsId:%d, groups:%d",
		param.StudentUid, param.CourseId, param.LeadsId, len(rsp.GroupList))

	return rsp, nil
}

// initCourseLessonInfos 初始化课程章节信息
func (s keyBehaviorService) initCourseLessonInfos(ctx *gin.Context, data *KeyBehaviorData) error {
	courseInfos, err := dal.GetCourseLessonInfoByCourseIds(ctx, []int64{int64(data.CourseId)})
	if err != nil {
		return fmt.Errorf("获取课程信息失败: %v", err)
	}

	courseIdStr := strconv.Itoa(data.CourseId)
	courseInfo, exists := courseInfos[courseIdStr]
	if !exists {
		return fmt.Errorf("获取章节信息错误: courseId=%d", data.CourseId)
	}

	data.CourseInfo = courseInfo
	data.LessonInfos = courseInfo.LessonList
	data.LessonCnt = len(courseInfo.LessonList)

	zlog.Infof(ctx, "initCourseLessonInfos success, courseId:%d, lessonCnt:%d", data.CourseId, data.LessonCnt)
	return nil
}

// initLeadsEsData 初始化Leads ES数据
func (s keyBehaviorService) initLeadsEsData(ctx *gin.Context, data *KeyBehaviorData) error {
	if data.LeadsId == 0 {
		zlog.Warnf(ctx, "initLeadsEsData skip, leadsId is 0")
		data.LeadsEsData = make(map[string]interface{})
		return nil
	}

	// 创建数据查询实例
	dataQuerySingleton := dataQuery.New()

	// 添加需要的字段
	dataQuerySingleton.AddFields(ctx, dataQuery.DataSourceLeads, []string{
		"leads_id", "lpc_uid", "course_id", "placement_test_submit_detail",
		"attend_num", "lbp_attend_num", "finish_num", "lbp_finish_num",
	})

	// 获取ES数据
	esDataMap, err := dataQuerySingleton.GetEsLeadsData(ctx, int64(data.PersonUid), int64(data.CourseId), []int64{int64(data.LeadsId)})
	if err != nil {
		zlog.Warnf(ctx, "GetEsLeadsData failed, leadsId:%d, err:%v", data.LeadsId, err)
		data.LeadsEsData = make(map[string]interface{})
		return nil
	}

	// 将结果存储到data中
	if esData, exists := esDataMap[int64(data.LeadsId)]; exists && esData != nil {
		// 转换为map[string]interface{}
		data.LeadsEsData = map[string]interface{}{
			"placement_test_submit_detail": esData.PlacementTestSubmitDetail,
			"attend_num":                   esData.AttendNum,
			"lbp_attend_num":               esData.LbpAttendNum,
			"finish_num":                   esData.FinishNum,
			"lbp_finish_num":               esData.LbpFinishNum,
		}
	} else {
		data.LeadsEsData = make(map[string]interface{})
	}

	zlog.Infof(ctx, "initLeadsEsData success, leadsId:%d, hasPlacementTest:%v",
		data.LeadsId, data.LeadsEsData["placement_test_submit_detail"] != nil)
	return nil
}

// initLpcLUData 初始化LPC学习数据
func (s keyBehaviorService) initLpcLUData(ctx *gin.Context, data *KeyBehaviorData) error {
	if data.CourseId == 0 || data.StudentUid == 0 {
		zlog.Warnf(ctx, "initLpcLUData skip, courseId:%d, studentUid:%d", data.CourseId, data.StudentUid)
		return nil
	}

	// 需要的字段
	fields := []string{"lpc_uid", "course_id", "student_uid", "lesson_id", "leads_id", "playback_time"}

	// 使用dataproxy API获取LPC数据
	dataproxyClient := dataproxy.NewClient()
	params := dataproxy.GetLpcListByCourseStudentParam{
		CourseId:   int64(data.CourseId),
		StudentUid: int64(data.StudentUid),
		Fields:     fields,
	}

	lpcDataList, err := dataproxyClient.GetLpcListByCourseStudent(ctx, params)
	if err != nil {
		// 与PHP版本保持一致：API调用失败时记录警告但继续执行，设置空数组
		zlog.Warnf(ctx, "GetLpcListByCourseStudent failed, err:%v", err)
		data.LpcLUData = make(map[int64]*das.StudentLessonInfo)
		return nil
	}

	// 构建LpcLUData映射，key为lessonId
	data.LpcLUData = make(map[int64]*das.StudentLessonInfo)
	for _, lpcData := range lpcDataList {
		// 转换为das.StudentLessonInfo格式以保持兼容性
		studentLessonInfo := &das.StudentLessonInfo{
			StudentUid:       lpcData.StudentUid,
			LessonId:         lpcData.LessonId,
			PlaybackDuration: lpcData.PlaybackTime, // playback_time对应playbackDuration
			IsAttended:       0,                    // 默认值，后续可从其他地方获取
			IsFinished:       0,                    // 默认值，后续可从其他地方获取
		}
		data.LpcLUData[lpcData.LessonId] = studentLessonInfo
	}

	zlog.Infof(ctx, "initLpcLUData success, studentUid:%d, courseId:%d, dataCount:%d",
		data.StudentUid, data.CourseId, len(data.LpcLUData))
	return nil
}

// initPreCompliTestData 初始化摸底测数据 - 按照PHP版本逻辑实现
func (s keyBehaviorService) initPreCompliTestData(ctx *gin.Context, data *KeyBehaviorData) error {
	data.PreCompliTestData = false

	// 1. 获取摸底测科目列表（对应PHP中的$subjectList）
	cpuId := data.CourseInfo.CpuId
	if cpuId == 0 {
		return nil
	}

	subjectList, err := examcore.NewClient().GetBottomTestSubjectList(ctx, cpuId)
	if err != nil {
		zlog.Warnf(ctx, "GetBottomTestSubjectList failed, cpuId:%d, err:%v", cpuId, err)
		return nil
	}

	// 2. 从ES数据中获取placement_test_submit_detail
	placementTestDetail, exists := data.LeadsEsData["placement_test_submit_detail"]
	if !exists || placementTestDetail == nil {
		// 如果没有ES数据但有科目列表，按照PHP逻辑仍需检查科目完整性
		if len(subjectList) > 0 {
			// 没有任何提交记录，认为未完成
			data.PreCompliTestData = false
		}
		zlog.Infof(ctx, "initPreCompliTestData no ES data, studentUid:%d, subjectCount:%d, result:%v",
			data.StudentUid, len(subjectList), data.PreCompliTestData)
		return nil
	}

	// 调试日志：原始摸底测数据
	components.DebugfWithJSON(ctx, "initPreCompliTestData raw data", map[string]interface{}{
		"subjectList":                  subjectList,
		"placement_test_submit_detail": placementTestDetail,
	})

	// 3. 解析placement_test_submit_detail数据
	detailMap, ok := placementTestDetail.(map[string]interface{})
	if !ok {
		zlog.Warnf(ctx, "initPreCompliTestData invalid data format, studentUid:%d", data.StudentUid)
		return nil
	}

	// 4. 按照PHP逻辑构建数据结构：先处理ES中的实际提交记录
	subjectDataMap := make(map[int]map[string]interface{})

	// 确定默认科目ID（当只有一个科目时）
	var defSubjectId int = 0
	if len(subjectList) == 1 {
		for _, subjectId := range subjectList {
			defSubjectId = subjectId
			break
		}
	}

	// 处理ES中的实际提交记录
	for _, value := range detailMap {
		if valueMap, ok := value.(map[string]interface{}); ok {
			// 检查is_submit_before_course字段
			isSubmitBeforeCourse := false
			if isSubmit, exists := valueMap["is_submit_before_course"]; exists {
				if submitVal, ok := isSubmit.(float64); ok {
					isSubmitBeforeCourse = submitVal > 0
				} else if submitVal, ok := isSubmit.(bool); ok {
					isSubmitBeforeCourse = submitVal
				}
			}

			// 如果未在规定时间提交，跳过
			if !isSubmitBeforeCourse {
				continue
			}

			// 获取科目ID，如果为空则使用默认科目ID
			subjectId := defSubjectId
			if subjectIdRaw, exists := valueMap["subject_id"]; exists {
				if id, ok := subjectIdRaw.(float64); ok && id > 0 {
					subjectId = int(id)
				}
			}

			// 处理分数 - 按照PHP逻辑
			firstScore := -1.0
			secondScore := -1.0

			if firstScoreRaw, exists := valueMap["first_score"]; exists {
				if score, ok := firstScoreRaw.(float64); ok {
					if score >= 0 {
						firstScore = math.Ceil(score / 10)
					} else {
						firstScore = -1
					}
				}
			}

			if secondScoreRaw, exists := valueMap["second_score"]; exists {
				if score, ok := secondScoreRaw.(float64); ok {
					if score >= 0 {
						secondScore = math.Ceil(score / 10)
					} else {
						secondScore = -1
					}
				}
			}

			// 存储科目数据
			subjectDataMap[subjectId] = map[string]interface{}{
				"isSubmitBeforeCourse":     isSubmitBeforeCourse,
				"placementTestScoreFirst":  firstScore,
				"placementTestScoreSecond": secondScore,
			}
		}
	}

	// 5. 按照PHP逻辑：为subjectList中的每个科目补全默认数据结构
	for _, subjectId := range subjectList {
		if _, exists := subjectDataMap[subjectId]; !exists {
			// 为缺失的科目创建默认数据结构
			subjectDataMap[subjectId] = map[string]interface{}{
				"isSubmitBeforeCourse":     0,
				"placementTestScoreFirst":  -1.0,
				"placementTestScoreSecond": -1.0,
			}
		}
	}

	// 6. 检查完成状态：所有科目都必须有有效分数
	allTestsComplete := true
	hasValidTests := len(subjectDataMap) > 0

	for subjectId, subjectData := range subjectDataMap {
		firstScore := subjectData["placementTestScoreFirst"].(float64)
		secondScore := subjectData["placementTestScoreSecond"].(float64)

		// 按照PHP逻辑：如果任一科目的两个分数都为-1，则认为未完成
		if firstScore == -1 && secondScore == -1 {
			allTestsComplete = false
			zlog.Infof(ctx, "initPreCompliTestData subject incomplete, subjectId:%d, firstScore:%v, secondScore:%v",
				subjectId, firstScore, secondScore)
			break
		}
	}

	// 7. 最终结果：只有存在有效测试且所有测试都完成时，才标记为true
	if hasValidTests && allTestsComplete {
		data.PreCompliTestData = true
	}

	// 调试日志：摸底测处理完成
	components.DebugfWithJSON(ctx, "initPreCompliTestData processing completed", map[string]interface{}{
		"subjectList":      subjectList,
		"subjectDataMap":   subjectDataMap,
		"hasValidTests":    hasValidTests,
		"allTestsComplete": allTestsComplete,
		"finalResult":      data.PreCompliTestData,
	})

	zlog.Infof(ctx, "initPreCompliTestData success, studentUid:%d, subjectCount:%d, hasValidTests:%v, allComplete:%v, result:%v",
		data.StudentUid, len(subjectList), hasValidTests, allTestsComplete, data.PreCompliTestData)
	return nil
}

// initNeedSurveyStatus 初始化挖需问卷状态
func (s keyBehaviorService) initNeedSurveyStatus(ctx *gin.Context, data *KeyBehaviorData) error {
	data.NeedSurveyStatus = false

	exists, err := models.TblAnswerDao.CheckNeedSurveyStatus(ctx, int64(data.CourseId), int64(data.StudentUid))
	if err != nil {
		zlog.Warnf(ctx, "CheckNeedSurveyStatus failed, courseId:%d, studentUid:%d, err:%v",
			data.CourseId, data.StudentUid, err)
		return nil
	}

	data.NeedSurveyStatus = exists
	zlog.Infof(ctx, "initNeedSurveyStatus success, studentUid:%d, courseId:%d, result:%v",
		data.StudentUid, data.CourseId, data.NeedSurveyStatus)
	return nil
}

// initOrderStatus 初始化预约问卷状态
func (s keyBehaviorService) initOrderStatus(ctx *gin.Context, data *KeyBehaviorData) error {
	data.OrderStatus = false

	// 根据courseId获取绑定信息，找到surveyType=2（预约问卷）对应的surveyId
	bindInfoList, err := models.TblBindInfoDao.GetBindInfoByCourseId(ctx, int64(data.CourseId), 1, nil)
	if err != nil {
		zlog.Warnf(ctx, "GetBindInfoByCourseId failed, courseId:%d, err:%v", data.CourseId, err)
		return nil
	}

	var surveyId int64 = 0
	for _, bindInfo := range bindInfoList {
		if bindInfo.SurveyType == models.SurveyTypeWenjuan { // 预约问卷 surveyType=2
			if id, err := strconv.ParseInt(bindInfo.SurveyID, 10, 64); err == nil {
				surveyId = id
				break
			}
		}
	}

	if surveyId == 0 {
		zlog.Infof(ctx, "no order survey found for courseId:%d", data.CourseId)
		return nil
	}

	// 查询tblAnswer表，检查学生是否有surveyType=2的问卷答案
	exists, err := models.TblAnswerDao.CheckAnswerExists(ctx, surveyId, int64(data.StudentUid), models.SurveyTypeWenjuan)
	if err != nil {
		zlog.Warnf(ctx, "CheckAnswerExists failed, surveyId:%d, studentUid:%d, err:%v", surveyId, data.StudentUid, err)
		return nil
	}

	data.OrderStatus = exists
	zlog.Infof(ctx, "initOrderStatus success, studentUid:%d, courseId:%d, surveyId:%d, result:%v",
		data.StudentUid, data.CourseId, surveyId, data.OrderStatus)
	return nil
}

// initFnOrderStatus 初始化蜂鸟预约问卷状态
func (s keyBehaviorService) initFnOrderStatus(ctx *gin.Context, data *KeyBehaviorData) error {
	data.FnOrderStatus = false

	// 根据courseId获取绑定信息，找到surveyType=3（蜂鸟预约问卷）对应的surveyId
	bindInfoList, err := models.TblBindInfoDao.GetBindInfoByCourseId(ctx, int64(data.CourseId), 1, nil)
	if err != nil {
		zlog.Warnf(ctx, "GetBindInfoByCourseId failed, courseId:%d, err:%v", data.CourseId, err)
		return nil
	}

	var actId int64 = 0
	for _, bindInfo := range bindInfoList {
		if bindInfo.SurveyType == models.SurveyTypeFengniao { // 蜂鸟预约问卷 surveyType=3
			if surveyId, err := strconv.ParseInt(bindInfo.SurveyID, 10, 64); err == nil {
				actId = surveyId
				break
			}
		}
	}

	if actId == 0 {
		zlog.Infof(ctx, "no fengniao survey found for courseId:%d", data.CourseId)
		return nil
	}

	// 检查学生是否有对应的蜂鸟活动记录
	exists, err := models.TblFnActiveRecordDao.CheckFnActiveExists(ctx, actId, 1, int64(data.StudentUid))
	if err != nil {
		zlog.Warnf(ctx, "CheckFnActiveExists failed, actId:%d, studentUid:%d, err:%v",
			actId, data.StudentUid, err)
		return nil
	}

	data.FnOrderStatus = exists
	zlog.Infof(ctx, "initFnOrderStatus success, studentUid:%d, courseId:%d, actId:%d, result:%v",
		data.StudentUid, data.CourseId, actId, data.FnOrderStatus)
	return nil
}

// initAttendNum 初始化到课数据
func (s keyBehaviorService) initAttendNum(ctx *gin.Context, data *KeyBehaviorData) error {
	attendNum := 0

	// 按照PHP逻辑：优先从ES数据获取attend_num和lbp_attend_num
	if attendNumRaw, exists := data.LeadsEsData["attend_num"]; exists {
		if num, ok := attendNumRaw.(float64); ok {
			attendNum += int(num)
		}
	}

	if lbpAttendNumRaw, exists := data.LeadsEsData["lbp_attend_num"]; exists {
		if num, ok := lbpAttendNumRaw.(float64); ok {
			attendNum += int(num)
		}
	}

	data.AttendNum = attendNum
	zlog.Infof(ctx, "initAttendNum success, studentUid:%d, attendNum:%d (from ES data)", data.StudentUid, data.AttendNum)
	return nil
}

// initFinishNum 初始化完课数据
func (s keyBehaviorService) initFinishNum(ctx *gin.Context, data *KeyBehaviorData) error {
	finishNum := 0

	// 按照PHP逻辑：优先从ES数据获取finish_num和lbp_finish_num
	if finishNumRaw, exists := data.LeadsEsData["finish_num"]; exists {
		if num, ok := finishNumRaw.(float64); ok {
			finishNum += int(num)
		}
	}

	if lbpFinishNumRaw, exists := data.LeadsEsData["lbp_finish_num"]; exists {
		if num, ok := lbpFinishNumRaw.(float64); ok {
			finishNum += int(num)
		}
	}

	data.FinishNum = finishNum
	zlog.Infof(ctx, "initFinishNum success, studentUid:%d, finishNum:%d (from ES data)", data.StudentUid, data.FinishNum)
	return nil
}

// initLpcPlayStatus 初始化LPC回放状态
func (s keyBehaviorService) initLpcPlayStatus(ctx *gin.Context, data *KeyBehaviorData) error {
	count := 0
	currentTime := time.Now().Unix()

	// 调试日志：开始处理回放状态
	components.DebugfWithJSONAndCount(ctx, "initLpcPlayStatus started, totalCount: %d, data: %s", len(data.LessonInfos)+len(data.LpcLUData), map[string]interface{}{
		"lessonCount":  len(data.LessonInfos),
		"lpcDataCount": len(data.LpcLUData),
		"lessonInfos":  data.LessonInfos,
	})

	// 按照PHP逻辑：遍历lessonInfos，检查对应的lpcLUData中的playback_time
	for lessonIdStr, lesson := range data.LessonInfos {
		lessonStartTime := int64(lesson.StartTime)
		if lessonStartTime > currentTime {
			continue // 课程还未开始
		}

		// 从字符串转换为int64
		lessonId, err := strconv.ParseInt(lessonIdStr, 10, 64)
		if err != nil {
			continue
		}

		// 获取对应的LPC学习数据
		if lessonInfo, exists := data.LpcLUData[lessonId]; exists && lessonInfo != nil {
			// 检查回放时长（5分钟 = 300秒）
			// PlaybackDuration字段对应PHP版本中的playback_time字段
			playbackDuration := lessonInfo.PlaybackDuration
			if playbackDuration >= 300 {
				count++
			}

			// 调试日志：单个lesson的回放状态检查
			components.DebugfWithJSON(ctx, "initLpcPlayStatus lesson check: %s", map[string]interface{}{
				"lessonId":         lessonId,
				"startTime":        lessonStartTime,
				"playbackDuration": playbackDuration,
				"qualified":        playbackDuration >= 300,
				"currentCount":     count,
			})
		} else {
			// 调试日志：没有找到对应的LPC数据
			components.DebugfWithJSON(ctx, "initLpcPlayStatus no lpc data found for lessonId: %s", lessonId)
		}
	}

	data.PlayStatusCnt = count

	// 调试日志：回放状态处理完成
	components.DebugfWithJSON(ctx, "initLpcPlayStatus completed, finalCount: %s", count)

	zlog.Infof(ctx, "initLpcPlayStatus success, studentUid:%d, playStatusCnt:%d", data.StudentUid, data.PlayStatusCnt)
	return nil
}

// initPreOrderInfos 初始化预支付数据
func (s keyBehaviorService) initPreOrderInfos(ctx *gin.Context, data *KeyBehaviorData) error {
	// 获取预订单信息
	preOrderList, err := models.PreOrderListDao.GetAllStuPreOrderInfos(ctx, []int64{int64(data.StudentUid)})
	if err != nil {
		zlog.Warnf(ctx, "GetAllStuPreOrderInfos failed, err:%v", err)
		data.PreOrderStatus = 0
		return nil
	}

	// 检查是否有状态为3（已支付）的预订单
	for _, order := range preOrderList {
		if order.StudentUid == int64(data.StudentUid) && order.Status == 3 {
			data.PreOrderStatus = 1
			break
		}
	}

	zlog.Infof(ctx, "initPreOrderInfos success, studentUid:%d, orderCount:%d, result:%d",
		data.StudentUid, len(preOrderList), data.PreOrderStatus)
	return nil
}

// formatData 格式化数据，按年级生成不同的分组
func (s keyBehaviorService) formatData(ctx *gin.Context, data *KeyBehaviorData) outputStudent.KeyBehaviorOutput {
	grade := data.CourseInfo.MainGradeId

	// 调试日志：开始格式化数据
	components.DebugfWithJSON(ctx, "formatData started: %s", map[string]interface{}{
		"grade":             grade,
		"preCompliTestData": data.PreCompliTestData,
		"needSurveyStatus":  data.NeedSurveyStatus,
		"orderStatus":       data.OrderStatus,
		"fnOrderStatus":     data.FnOrderStatus,
		"attendNum":         data.AttendNum,
		"finishNum":         data.FinishNum,
		"playStatusCnt":     data.PlayStatusCnt,
		"preOrderStatus":    data.PreOrderStatus,
	})

	var groups []outputStudent.KeyBehaviorGroup

	// 小学生 (grade: 1,11-16)
	if s.isElementaryGrade(grade) {
		components.DebugfWithJSON(ctx, "formatData processing elementary grade: %s", grade)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdTest, s.createPreCompliTestItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdSurvey, s.createNeedSurveyStatusItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdSurvey, s.createOrderStatusItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdSurvey, s.createFnOrderStatusItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdAttend, s.createAttendItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdFinish, s.createFinishItem(data))...)
	} else if s.isMiddleGrade(grade) {
		// 初中生 (grade: 2-4,20)
		components.DebugfWithJSON(ctx, "formatData processing middle grade: %s", grade)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdSurvey, s.createFnOrderStatusItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdAttend, s.createAttendItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdFinish, s.createFinishItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdPlayback, s.createPlayBackItem(data))...)
	} else if s.isHighGrade(grade) {
		// 高中生 (grade: 5-7,30)
		components.DebugfWithJSON(ctx, "formatData processing high grade: %s", grade)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdAttend, s.createAttendItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdFinish, s.createFinishItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdPlayback, s.createPlayBackItem(data))...)
		groups = append(groups, s.createGroup(consts.KeyBehaviorGroupIdPreorder, s.createPreOrderItem(data))...)
	}

	// 合并相同分组并计算分组的isLight状态
	groups = s.mergeAndCalculateGroups(ctx, groups)

	return outputStudent.KeyBehaviorOutput{
		GroupList: groups,
	}
}

// Helper functions for grade checking
func (s keyBehaviorService) isElementaryGrade(grade int64) bool {
	for _, g := range consts.ElementaryGrades {
		if grade == g {
			return true
		}
	}
	return false
}

func (s keyBehaviorService) isMiddleGrade(grade int64) bool {
	for _, g := range consts.MiddleGrades {
		if grade == g {
			return true
		}
	}
	return false
}

func (s keyBehaviorService) isHighGrade(grade int64) bool {
	for _, g := range consts.HighGrades {
		if grade == g {
			return true
		}
	}
	return false
}

// Helper functions for creating items and groups
func (s keyBehaviorService) createGroup(groupId int, items ...outputStudent.KeyBehaviorGroupItem) []outputStudent.KeyBehaviorGroup {
	if len(items) == 0 {
		return nil
	}

	groupName := consts.KeyBehaviorGroupMap[groupId]
	return []outputStudent.KeyBehaviorGroup{
		{
			GroupId:   groupId,
			GroupName: groupName,
			IsLight:   0, // 将由mergeAndCalculateGroups计算
			Element:   items,
		},
	}
}

func (s keyBehaviorService) createPreCompliTestItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.PreCompliTestData {
		isLight = 1
	}
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdPreComplites,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdPreComplites],
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createNeedSurveyStatusItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.NeedSurveyStatus {
		isLight = 1
	}
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdNeedSurveyStatus,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdNeedSurveyStatus],
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createOrderStatusItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.OrderStatus {
		isLight = 1
	}
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdOrderStatus,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdOrderStatus],
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createFnOrderStatusItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.FnOrderStatus {
		isLight = 1
	}
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdFnOrderStatus,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdFnOrderStatus],
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createAttendItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.AttendNum > 0 {
		isLight = 1
	}
	label := s.formatLabel(data.AttendNum, data.LessonCnt)
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdAttend,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdAttend] + label,
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createFinishItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.FinishNum > 0 {
		isLight = 1
	}
	label := s.formatLabel(data.FinishNum, data.LessonCnt)
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdFinish,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdFinish] + label,
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createPlayBackItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.PlayStatusCnt > 0 {
		isLight = 1
	}
	label := s.formatLabel(data.PlayStatusCnt, data.LessonCnt)
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdPlayback,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdPlayback] + label,
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) createPreOrderItem(data *KeyBehaviorData) outputStudent.KeyBehaviorGroupItem {
	isLight := 0
	if data.PreOrderStatus == 1 {
		isLight = 1
	}
	return outputStudent.KeyBehaviorGroupItem{
		ItemId:   consts.KeyBehaviorItemIdPreorder,
		ItemName: consts.KeyBehaviorItemMap[consts.KeyBehaviorItemIdPreorder],
		IsLight:  isLight,
	}
}

func (s keyBehaviorService) formatLabel(numerator, denominator int) string {
	return fmt.Sprintf("(%d/%d)", numerator, denominator)
}

// mergeAndCalculateGroups 合并相同分组并计算分组的isLight状态
func (s keyBehaviorService) mergeAndCalculateGroups(ctx *gin.Context, groups []outputStudent.KeyBehaviorGroup) []outputStudent.KeyBehaviorGroup {
	groupMap := make(map[int]*outputStudent.KeyBehaviorGroup)

	// 调试日志：开始合并分组
	components.DebugfWithJSONAndCount(ctx, "mergeAndCalculateGroups started, inputGroupCount: %d, groups: %s", len(groups), groups)

	// 合并相同分组的元素
	for _, group := range groups {
		if existingGroup, exists := groupMap[group.GroupId]; exists {
			// 调试日志：合并到现有分组
			components.DebugfWithJSON(ctx, "mergeAndCalculateGroups merging to existing group: %s", map[string]interface{}{
				"groupId":     group.GroupId,
				"newElements": group.Element,
			})
			existingGroup.Element = append(existingGroup.Element, group.Element...)
		} else {
			// 调试日志：创建新分组
			components.DebugfWithJSON(ctx, "mergeAndCalculateGroups creating new group: %s", map[string]interface{}{
				"groupId":   group.GroupId,
				"groupName": group.GroupName,
				"elements":  group.Element,
			})
			groupCopy := group
			groupMap[group.GroupId] = &groupCopy
		}
	}

	// 计算每个分组的isLight状态并转换为slice
	result := make([]outputStudent.KeyBehaviorGroup, 0, len(groupMap))
	for _, group := range groupMap {
		isLight := 1
		for _, element := range group.Element {
			if element.IsLight == 0 {
				isLight = 0
				break
			}
		}
		group.IsLight = isLight

		// 调试日志：分组isLight计算结果
		components.DebugfWithJSON(ctx, "mergeAndCalculateGroups group isLight calculated: %s", map[string]interface{}{
			"groupId":   group.GroupId,
			"groupName": group.GroupName,
			"isLight":   group.IsLight,
			"elements":  group.Element,
		})

		result = append(result, *group)
	}

	// 调试日志：合并完成
	components.DebugfWithJSONAndCount(ctx, "mergeAndCalculateGroups completed, finalGroupCount: %d, result: %s", len(result), result)

	return result
}

// getLeadsIdAndPersonUidByStudentUidCourseId 根据学生UID和课程ID获取线索ID和PersonUID
func (s keyBehaviorService) getLeadsIdAndPersonUidByStudentUidCourseId(ctx *gin.Context, studentUid, courseId int) (int, int, error) {
	// 创建allocate客户端
	allocateClient := allocate.NewClient()

	// 调用批量获取leads信息的API
	leadsInfoList, err := allocateClient.GetLeadsByBatchCourseIdUid(ctx, []int64{int64(courseId)}, int64(studentUid))
	if err != nil {
		zlog.Warnf(ctx, "GetLeadsByBatchCourseIdUid failed, studentUid:%d, courseId:%d, err:%v", studentUid, courseId, err)
		return 0, 0, err
	}

	if len(leadsInfoList) == 0 {
		zlog.Warnf(ctx, "GetLeadsByBatchCourseIdUid returned empty results, studentUid:%d, courseId:%d", studentUid, courseId)
		return 0, 0, nil
	}

	// 按照allocTime倒序排序，取最新的一个
	sort.Slice(leadsInfoList, func(i, j int) bool {
		return leadsInfoList[i].AllocTime > leadsInfoList[j].AllocTime
	})

	leadsId := int(leadsInfoList[0].LeadsId)
	personUid := int(leadsInfoList[0].PersonUid)
	zlog.Infof(ctx, "getLeadsIdAndPersonUidByStudentUidCourseId success, studentUid:%d, courseId:%d, leadsId:%d, personUid:%d",
		studentUid, courseId, leadsId, personUid)

	return leadsId, personUid, nil
}
