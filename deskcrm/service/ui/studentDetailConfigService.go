package ui

import (
	"deskcrm/api/tower"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/output/outputStudentDetailConfig"
	"errors"
	"slices"

	"github.com/gin-gonic/gin"
)

var StudentDetailConfigService studentDetailConfigService

type studentDetailConfigService struct {
}

// GetDetailConfig 获取学员详情页配置
func (s studentDetailConfigService) GetDetailConfig(ctx *gin.Context, courseId int64) (*outputStudentDetailConfig.DetailConfigResponse, error) {
	if courseId <= 0 {
		return nil, errors.New("参数错误")
	}

	// 调用Tower API获取课程信息
	towerClient := tower.NewClient()
	courseInfoMap, err := towerClient.GetCourseInfo(ctx, []int64{courseId})
	if err != nil {
		return nil, errors.New("获取课程信息错误")
	}

	courseInfo, exists := courseInfoMap[courseId]
	if !exists {
		return nil, errors.New("课程信息不存在")
	}

	// 获取课程性质
	coursePriceTag := courseInfo.CoursePriceTag

	// 初始化配置，常显模块默认为1
	config := &outputStudentDetailConfig.DetailConfigResponse{
		UserInfo:        1, // 用户信息 (常显)
		TagInfo:         1, // 标签信息 (常显)
		Remark:          1, // 备注 (常显)
		CourseRecord:    1, // 课程记录 (常显)
		InterviewRecord: 1, // 维系记录 (常显)
		// 其他字段默认为0，根据课程性质决定
		UserLayerLpc: 0,
		UserLayerFD:  0,
		CourseTotal:  0,
		CustomField:  0,
		FocusLpc:     0,
		ContinueWish: 0,
		Behavior:     0,
	}

	// 根据课程性质配置各模块显示状态
	s.ConfigureModulesByCoursePriceTag(config, coursePriceTag)

	return config, nil
}

// ConfigureModulesByCoursePriceTag 根据课程性质配置模块显示状态 (导出供测试使用)
func (s studentDetailConfigService) ConfigureModulesByCoursePriceTag(config *outputStudentDetailConfig.DetailConfigResponse, coursePriceTag int) {
	// 自定义字段：辅导课程性质 - 辅导小学规划课程性质
	fdPriceTags := s.diffIntSlice(consts.FdCoursePriceTag, consts.FdXxgjCoursePriceTag)
	if slices.Contains(fdPriceTags, coursePriceTag) {
		config.CustomField = 1
	}

	// lpc用户分层：小学X课程性质
	if slices.Contains(consts.LpXiaoxueXCoursePriceTag, coursePriceTag) {
		config.UserLayerLpc = 1
	}

	// 辅导用户分层：辅导课程性质 - 辅导小学规划课程性质 - 辅导小学低段拓科
	fdUserLayerTags := s.diffIntSlice(consts.FdCoursePriceTag, consts.FdXxgjCoursePriceTag)
	fdUserLayerTags = s.diffIntSlice(fdUserLayerTags, consts.FdLittleKidExtentSubject)
	if slices.Contains(fdUserLayerTags, coursePriceTag) {
		config.UserLayerFD = 1
	}

	// 课程汇总：LPC课程性质
	if slices.Contains(consts.LpcCoursePriceTag, coursePriceTag) {
		config.CourseTotal = 1
	}

	// 学员关注状态：LPC课程性质
	if slices.Contains(consts.LpcCoursePriceTag, coursePriceTag) {
		config.FocusLpc = 1
	}

	// 关键行为：LPC课程性质
	if slices.Contains(consts.LpcCoursePriceTag, coursePriceTag) {
		config.Behavior = 1
	}

	// 续报意愿：辅导课程性质 - 辅导小学规划课程性质 - 辅导小学低段拓科
	continueWishTags := s.diffIntSlice(consts.FdCoursePriceTag, consts.FdXxgjCoursePriceTag)
	continueWishTags = s.diffIntSlice(continueWishTags, consts.FdLittleKidExtentSubject)
	if slices.Contains(continueWishTags, coursePriceTag) {
		config.ContinueWish = 1
	}
}

// diffIntSlice 计算两个整数切片的差集 (slice1 - slice2)
func (s studentDetailConfigService) diffIntSlice(slice1, slice2 []int) []int {
	// 创建第二个切片的映射以提高查找效率
	excludeMap := make(map[int]bool)
	for _, v := range slice2 {
		excludeMap[v] = true
	}

	// 构建差集
	var result []int
	for _, v := range slice1 {
		if !excludeMap[v] {
			result = append(result, v)
		}
	}

	return result
}
