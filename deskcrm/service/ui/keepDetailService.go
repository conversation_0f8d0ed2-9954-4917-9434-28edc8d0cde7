package ui

import (
	"deskcrm/api/dal"
	"deskcrm/api/dau"
	"deskcrm/api/tag"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputKeepDetail"
	"deskcrm/controllers/http/ui/output/outputKeepDetail"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/innerapi/course"
	"deskcrm/stru/keepDetail"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mesh"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/muse"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"sort"
	"strconv"
	"strings"
	"time"
)

var KeepDetailService keepDetailService

type keepDetailService struct {
}

func (s keepDetailService) GetStudentBind(ctx *gin.Context, courseId, studentUid int64) (resp []keepDetail.TagEntity, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetStudentBind error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetStudentBind panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	// get course info
	courseInfo, err := dal.GetKVByCourseId(ctx, courseId)
	if err != nil {
		return
	}

	var gradeId, subjectId int64
	if info, ok := courseInfo[strconv.Itoa(int(courseId))]; ok {
		gradeId = info.MainGradeId
		subjectId = info.MainSubjectId
	}

	// get tag service
	resp, err = tag.NewClient().GetStudentBind(ctx, courseId, studentUid, gradeId, subjectId)
	if err != nil {
		return nil, err
	}

	return
}

func (s keepDetailService) GetActiveWithBindData(ctx *gin.Context, courseId, studentUid, assistantUid int64, applyType int) (resp *tag.GetActiveResp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetActiveWithBindData error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetActiveWithBindData panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	// get course info
	courseInfo, err := dal.GetKVByCourseId(ctx, courseId)
	if err != nil {
		return
	}

	var gradeId, subjectId, newCourseType int64
	if info, ok := courseInfo[strconv.Itoa(int(courseId))]; ok {
		gradeId = info.MainGradeId
		subjectId = info.MainSubjectId
		newCourseType = info.NewCourseType
	}

	paramRules := map[string]int64{
		keepDetail.RULE_TYPE_COURSE_ID:   courseId,
		keepDetail.RULE_TYPE_COURSE_TYPE: newCourseType,
		keepDetail.RULE_TYPE_GRADE:       gradeId,
		keepDetail.RULE_TYPE_SUBJECT:     subjectId,
	}

	toString, err := json.MarshalToString(paramRules)
	if err != nil {
		return
	}

	// get tag service
	templateResp, err := tag.NewClient().GetActiveTemplate(ctx, applyType, toString)
	if err != nil || templateResp == nil {
		return templateResp, nil
	}

	if len(templateResp.Contents) == 0 {
		return
	}

	bindResp, err := tag.NewClient().GetStudentBind(ctx, courseId, studentUid, gradeId, subjectId)
	if err != nil {
		return nil, err
	}

	for templateKeyIdx, templateValue := range templateResp.Contents {
		tmpBind := make([]keepDetail.TagEntity, 0)
		copy(tmpBind, bindResp)

		for itemKeyIdx, itemValue := range templateValue.Items {
			for tagKeyIdx, tagValue := range itemValue.TagGroup {
				for bindKeyIdx, bindValue := range tmpBind {
					if templateValue.CID != bindValue.CId {
						tmpBind = append(tmpBind[:bindKeyIdx], tmpBind[bindKeyIdx+1:]...)
						continue
					}

					if tagValue.Code == bindValue.Code {
						// 标记已选中的标签
						templateResp.Contents[templateKeyIdx].Items[itemKeyIdx].TagGroup[tagKeyIdx].Selected = 1
						templateResp.Contents[templateKeyIdx].Items[itemKeyIdx].TagGroup[tagKeyIdx].IsSyncFromOthers = bindValue.CreatorId != assistantUid
						// 从临时绑定列表中移除已处理的项
						tmpBind = append(tmpBind[:bindKeyIdx], tmpBind[bindKeyIdx+1:]...)
					}
				}
			}
		}

		// 处理剩余未匹配的绑定项
		if len(tmpBind) > 0 {
			var otherTags []tag.Tag
			for _, value := range tmpBind {
				otherTags = append(otherTags, tag.Tag{
					Name:             value.Name,
					Code:             value.Code,
					IsSyncFromOthers: value.CreatorId != assistantUid,
					Selected:         1,
				})
			}

			// 添加"其他"分类
			if len(otherTags) > 0 {
				templateResp.Contents[templateKeyIdx].Items = append(templateResp.Contents[templateKeyIdx].Items, tag.Item{
					Name:     "其他",
					TagGroup: otherTags,
					IsMulti:  1,
				})
			}
		}
	}

	return templateResp, nil
}

func (s keepDetailService) GetCustomFieldOptions(ctx *gin.Context, courseId, studentUid int64) (resp []keepDetail.CustomFieldOptions, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetCustomFieldOptions error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetCustomFieldOptions panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	resp = make([]keepDetail.CustomFieldOptions, 0)

	// 1，course bind custom field
	// 2，student custom field
	customFieldData, err := dataQuery.New().GetCustomFieldData(ctx, courseId, []int64{studentUid})
	if err != nil {
		return nil, err
	}
	// 3，format
	fieldInfoMap := make(map[int64][]*models.CustomField)
	for _, item := range customFieldData.FieldInfos {
		fieldInfoMap[item.FirstClassId] = append(fieldInfoMap[item.FirstClassId], item)
	}

	studentFieldIdValueMap := make(map[int64]string)
	for _, item := range customFieldData.CustomData {
		studentFieldIdValueMap[item.FieldId] = item.Value
	}

	for firstClassId, info := range fieldInfoMap {
		list := make([]keepDetail.Item, 0)
		for _, val := range info {
			list = append(list, keepDetail.Item{
				Label:       val.Name,
				FieldID:     components.Util.GetCustomKey(ctx, val.ID, val.OptionType),
				FieldValue:  keepDetail.FormatFieldValue(val.OptionType, val.ID, studentFieldIdValueMap),
				Placeholder: "请标注",
				IsMultiple:  val.OptionType == 1,
				Options:     keepDetail.FormatOptions(val.FieldOption),
			})
		}
		resp = append(resp, keepDetail.CustomFieldOptions{
			FirstClassName: components.GetArkNewFieldTypeMap()[int(firstClassId)],
			List:           list,
		})
	}

	// 排序
	sort.Slice(resp, func(i, j int) bool {
		return resp[i].FirstClassName < resp[j].FirstClassName
	})

	return
}

func (s keepDetailService) GetStudentCallInfo(ctx *gin.Context, param inputKeepDetail.StudentCallInfoParams) (resp keepDetail.StudentCallInfo, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetStudentCallInfo error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetStudentCallInfo panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()
	resp = keepDetail.StudentCallInfo{
		Total:          0,
		Page:           param.Page,
		PageSize:       param.PageSize,
		CallDetailList: make([]keepDetail.CallDetail, 0),
	}

	paramTouchMisGo := &touchmisgo.GetCallRecordHistoryReq{
		ToUid:    []int64{int64(param.StudentUID)},
		Fields:   []string{"call_id", "call_result", "from_uid"},
		PageNum:  param.Page,
		PageSize: param.PageSize,
	}

	if param.PageTab == consts.PT_DETAIL_SELF {
		paramTouchMisGo.FromUid = param.AssistantUid
	}

	if param.PageTab == consts.PT_DETAIL_COURSE {
		paramTouchMisGo.CourseId = int64(param.CourseID)
	}

	// get call info from touch mis go
	list, err := touchmisgo.GetCallRecordHistoryList(ctx, paramTouchMisGo)
	if err != nil {
		return resp, err
	}

	if len(list.CallRecordList) == 0 {
		return
	}

	callList := make([]touchmisgo.CallRecordInfo, 0)
	for _, item := range list.CallRecordList {
		if param.PageTab == consts.PT_DETAIL_SELF {
			callList = append(callList, item)
		} else {
			// 不是只看自己，就返回接通的||老师自己的
			if item.CallResult == consts.RESULT_CALLEND || item.FromUid == param.AssistantUid {
				callList = append(callList, item)
			}
		}
	}

	if len(callList) == 0 {
		return
	}
	// format
	resp.CallDetailList, err = s.formatCallRes(ctx, param, callList)
	if err != nil {
		return
	}
	resp.Total = list.Total

	return
}

func (s keepDetailService) formatCallRes(ctx *gin.Context, param inputKeepDetail.StudentCallInfoParams, calls []touchmisgo.CallRecordInfo) (res []keepDetail.CallDetail, err error) {
	assistantUids := make([]int64, 0)
	assistantNameMap := make(map[string]string)
	staffNameMap := make(map[string]string)
	courseIds := make([]int64, 0)
	courseNameMap := make(map[string]string)
	res = make([]keepDetail.CallDetail, 0)

	museConsts, err := muse.GetCallOutConstList(ctx)
	if err != nil {
		return
	}

	for _, item := range calls {
		assistantUids = append(assistantUids, item.DeviceUid)
		if item.CallType != consts.CALL_TYPE {
			assistantUids = append(assistantUids, item.ToUid)
		}

		if param.PageType == consts.PT_DETAIL && item.CourseId != 0 {
			courseIds = append(courseIds, item.CourseId)
		}
	}

	assistantUids = components.Array.UniqueInt64(assistantUids)
	courseIds = components.Array.UniqueInt64(courseIds)
	// 辅导老师
	if len(assistantUids) > 0 || param.CallType == consts.TYPE_OUT {
		deviceList := make(map[string]*mesh.DeviceInfo)
		deviceList, err = mesh.GetDeviceInfoList(ctx, assistantUids)
		if err != nil {
			return
		}
		for id, info := range deviceList {
			assistantNameMap[id] = info.Nickname
			staffNameMap[id] = info.StaffName
		}
	}

	// 课程
	if param.PageType == consts.PT_DETAIL && len(courseIds) > 0 {
		courseBaseInfo := make(map[int64]dal.CourseInfo)
		courseBaseInfo, err = course.GetCourseDataInstance(ctx).GetBaseInfoByCourseIds(ctx, courseIds)
		if err != nil {
			return
		}
		for id, info := range courseBaseInfo {
			courseNameMap[cast.ToString(id)] = info.CourseName
		}
	}

	// 学生
	studentListMap, err := dau.GetStudents(ctx, []int64{int64(param.StudentUID)}, []string{"guardian", "studentUid"})
	if err != nil {
		return
	}
	guardian := ""
	if info, ok := studentListMap[int64(param.StudentUID)]; ok {
		guardian = info.Guardian
	}

	// format
	for _, item := range calls {
		var phone, studentPhone string
		if item.CallType == consts.TYPE_OUT {
			phone = item.FromPhone
			studentPhone = item.ToPhone
		} else {
			phone = item.ToPhone
			studentPhone = item.FromPhone
		}

		link, err := muse.GetRecordLink(ctx, item.RecordFile, item.ResourceType)
		if err != nil {
			return nil, err
		}

		personName := "账号已解绑"
		assistantName := ""
		if name, ok := staffNameMap[cast.ToString(item.DeviceUid)]; ok {
			personName = name
		}
		if name, ok := assistantNameMap[cast.ToString(item.DeviceUid)]; ok {
			assistantName = name
		}

		res = append(res, keepDetail.CallDetail{
			StartTime:    time.Unix(item.StartTime/1000, 0).Format("2006-01-02 15:04:05"),
			StopTime:     time.Unix(item.StopTime/1000, 0).Format("2006-01-02 15:04:05"),
			Status:       consts.CallResultValueMap[int(item.CallResult)],
			Department:   "辅导老师",
			Type:         consts.CallTypeValueMap[int(item.CallType)],
			StudentName:  guardian,
			Phone:        components.Util.HiddenPhone(phone),
			StudentPhone: components.Util.HiddenPhone(studentPhone),
			RecordFile:   link,
			SourceType:   s.getCallTypeText(museConsts, int(item.SourceType)),
			CallerName:   assistantName + "(" + personName + ")",
			CourseName:   courseNameMap[cast.ToString(item.CourseId)],
			CallMode:     int(item.CallMode),
		})
	}

	return
}

func (s keepDetailService) getCallTypeText(callConst muse.CallSystemConstResp, sourceType int) string {
	var callTypes []string

	for bitType, value := range callConst.SourceTypeValueMap {
		if (sourceType & value) != 0 {
			if description, exists := callConst.CallSourceValueMap[bitType]; exists {
				callTypes = append(callTypes, description)
			}
		}
	}

	if len(callTypes) > 0 {
		return strings.Join(callTypes, ",")
	}
	return ""
}

func (s keepDetailService) GetSchemaByCourseId(ctx *gin.Context, courseId int64) (schema *outputKeepDetail.GetSchemaByCourseIdResp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetSchemaByCourseId error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetSchemaByCourseId panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	schema = &outputKeepDetail.GetSchemaByCourseIdResp{Schema: ""}

	var schemaId int64
	// get by course id
	schemaRes, err := models.PageSchemaCourseBindDao.GetByCourseID(ctx, courseId, nil)
	if err != nil {
		return nil, err
	}
	if schemaRes != nil && schemaRes.SchemaID != 0 {
		schemaId = schemaRes.SchemaID
	} else {
		// get by course price tag
		var courseInfo *tower.GetCourseInfoRsp
		courseInfo, err = tower.GetCourseInfo(ctx, courseId)
		if err != nil || courseInfo == nil {
			return nil, err
		}

		coursePricetagSchemaRes, err := models.PageSchemaCoursePriceTagBindDao.GetByCoursePriceTagID(ctx, courseInfo.CoursePriceTag, nil)
		if err != nil {
			return nil, err
		}

		if coursePricetagSchemaRes != nil && coursePricetagSchemaRes.SchemaID != 0 {
			schemaId = coursePricetagSchemaRes.SchemaID
		}
	}

	// get detail
	if schemaId != 0 {
		schemaInfo, err := models.PageSchemaDao.GetByID(ctx, schemaId, nil)
		if err != nil {
			return nil, err
		}
		if schemaInfo != nil && schemaInfo.Schema != "" {
			schema.Schema = schemaInfo.Schema
		}
	}

	return
}

func (s keepDetailService) GetCallTypeList(ctx *gin.Context) (resp outputKeepDetail.GetCallTypeListResp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "GetCallTypeList error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "GetCallTypeList panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	resp = outputKeepDetail.GetCallTypeListResp{}

	config, err := muse.GetCallOutConstList(ctx)
	if err != nil {
		return
	}

	callSourceMap := config.CallSourceMap
	callSourceValueMap := config.CallSourceValueMap
	var callTypeArray = []int{
		callSourceMap["SOURCE_TYPE_HOME_VISIT"],
		callSourceMap["SOURCE_TYPE_11"],
		callSourceMap["SOURCE_TYPE_CONTINUE_COURSE"],
		// callSourceMap["SOURCE_TYPE_TMK"],
		callSourceMap["SOURCE_TYPE_16"],
		callSourceMap["SOURCE_TYPE_19"],
		callSourceMap["SOURCE_TYPE_23"],
		callSourceMap["SOURCE_TYPE_25"],
		callSourceMap["SOURCE_TYPE_OTHER"],
	}

	list := make([]outputKeepDetail.CallTypeItem, 0)

	for _, item := range callTypeArray {
		list = append(list, outputKeepDetail.CallTypeItem{
			Label: callSourceValueMap[cast.ToString(item)],
			Value: item,
		})
	}

	resp.CallTypeList = list

	return
}
