package lessonList

import (
	"deskcrm/api/dal"
	"deskcrm/components"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/libs/json"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory"
	struArk "deskcrm/stru/ark"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sync"
)

func NewLessonList() *lessonListService {
	c := &lessonListService{
		param:              &inputKeepDetail.LessonListParam{},
		lessonIds:          make([]int64, 0),
		lessonMap:          map[int64]*dal.LessonInfo{},
		fieldRuleMap:       map[string]*struLessonList.LessonRuleConfigStru{},
		implodeStudentUids: make([]int64, 0),
		lock:               &sync.Mutex{},
	}
	return c
}

type lessonListService struct {
	param              *inputKeepDetail.LessonListParam
	dataQueryPoint     *dataQuery.Singleton
	lessonIds          []int64
	lessonMap          map[int64]*dal.LessonInfo
	fieldRuleMap       map[string]*struLessonList.LessonRuleConfigStru //全部字段列规则
	allFilterKeys      []string                                        //全部筛选字段key
	sortKeys           []string                                        //排序字段key
	implodeStudentUids []int64
	sorts              []*struArk.SortsRule
	lock               *sync.Mutex
	GroupKey           string
}

type LessonListData struct {
	LessonList []map[string]interface{}
	lessonIDs  []int64
	Total      int
}

func (s *lessonListService) InitParam(ctx *gin.Context, param *inputKeepDetail.LessonListParam) (err error) {
	zlog.Debugf(ctx, "InitParam param:%+v", param)
	s.param = param

	sorts := make([]*struArk.SortsRule, 0)
	_ = json.UnmarshalFromString(s.param.Sorts, &sorts)
	s.sorts = append(s.sorts, sorts...)

	//push 默认规则
	s.sorts = append(s.sorts, &struArk.SortsRule{
		Key:  "startTime",
		Sort: "asc",
	})
	return
}

func (s *lessonListService) InitDataQueryPoint(ctx *gin.Context, dataQueryPoint *dataQuery.Singleton) (err error) {
	zlog.Debugf(ctx, "InitDataQueryPoint dataQueryPoint:%+v", dataQueryPoint)
	s.dataQueryPoint = dataQueryPoint
	return
}

func (s *lessonListService) InitLessonList(ctx *gin.Context, dataQueryPoint *dataQuery.Singleton) (err error) {
	//初始章节列表
	queryData, err := dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseId})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)

	for idx := range courseInfo.LessonList {
		lessonDetail := courseInfo.LessonList[idx]
		lessonID := int64(lessonDetail.LessonId)
		s.lessonIds = append(s.lessonIds, lessonID)
		s.lessonMap[lessonID] = &lessonDetail
	}
	return
}

func (s *lessonListService) initOutput(ctx *gin.Context, lessonIDs []int64) (output *struLessonList.LessonListOutput, err error) {
	zlog.Debugf(ctx, "initOutput lessonIds:%+v", lessonIDs)

	output = &struLessonList.LessonListOutput{
		LessonListSource: map[string]struLessonList.LessonListSource{},
		LessonListOutput: map[int64]map[string]interface{}{},
	}

	for _, lessonID := range lessonIDs {
		lesson, ok := s.lessonMap[lessonID]
		if !ok {
			continue
		}

		output.LessonListOutput[lessonID] = map[string]interface{}{
			"lessonID":  lessonID,
			"courseID":  s.param.CourseId,
			"startTime": lesson.StartTime,
		}
	}
	return
}

// InitLessonListConfig 初始化规则
func (s *lessonListService) InitLessonListConfig(ctx *gin.Context, forPreData bool) (err error) {
	lessonConfigMap := map[string]*struLessonList.LessonListGroupStru{}
	for _, lessonGroupConfig := range struLessonList.LessonListConfig {
		lessonConfigMap[lessonGroupConfig.GroupKey] = lessonGroupConfig
	}
	groupConfig := lessonConfigMap[s.param.Tab]

	keys := make([]string, 0)
	fieldRuleMap := map[string]*struLessonList.LessonRuleConfigStru{}
	for _, ruleConfig := range groupConfig.RuleList {
		keys = append(keys, ruleConfig.Key)
		fieldRuleMap[ruleConfig.Key] = ruleConfig
	}

	sortKeys := make([]string, 0)
	for _, detail := range s.sorts {
		sortKeys = append(sortKeys, detail.Key)
	}

	s.lock.Lock()
	s.fieldRuleMap = fieldRuleMap
	s.allFilterKeys = keys
	s.GroupKey = s.param.Tab
	s.lock.Unlock()
	return
}

func (s *lessonListService) GetLessonList(ctx *gin.Context) (lessonDataList *LessonListData, err error) {
	//初始化output
	output, _ := s.initOutput(ctx, s.lessonIds)

	lessonFactoryData, _ := s.getLessonDataFactoryData(ctx, s.allFilterKeys, s.lessonIds)
	_ = s.dataQueryWarming(ctx, lessonFactoryData) //dataquery字段预热

	lessonFactoryData, _ = s.getLessonDataFactoryData(ctx, s.sortKeys, s.lessonIds)
	_ = lessonDataFactory.FormatRouter.Exec(ctx, s.dataQueryPoint, lessonFactoryData, output)

	lessonList, err := s.outputToSliceAndSort(ctx, output)
	if err != nil {
		zlog.Errorf(ctx, "outputToSliceAndSort err:%s", err.Error())
	}

	//获取列表展示数据
	//只处理需要的区间数据
	offset, limit := s.param.Offset, s.param.Limit
	end := fwyyutils.MinInt64(int64(len(lessonList)), offset+limit)
	if end > offset {
		lessonList = lessonList[offset:end]
	} else {
		lessonList = []map[string]interface{}{}
	}

	lessonIDs := make([]int64, 0)
	for _, lessonDetail := range lessonList {
		lessonID := cast.ToInt64(lessonDetail["lessonID"])
		lessonIDs = append(lessonIDs, lessonID)
	}
	_ = s.resetLessonList(ctx, lessonIDs)

	//取数据只取数据字段key
	lessonFactoryData, _ = s.getLessonDataFactoryData(ctx, s.allFilterKeys, lessonIDs)
	output, _ = s.initOutput(ctx, lessonIDs) //初始化output,获取列表数据

	_ = lessonDataFactory.FormatRouter.Exec(ctx, s.dataQueryPoint, lessonFactoryData, output)

	lessonList, err = s.outputToSliceAndSort(ctx, output)
	if err != nil {
		zlog.Errorf(ctx, "outputToSliceAndSort err:%s", err.Error())
	}

	lessonList = s.addDataSourceComment(ctx, lessonList, output.LessonListSource)

	return &LessonListData{
		LessonList: lessonList,
		lessonIDs:  s.lessonIds,
		Total:      len(s.lessonMap),
	}, nil
}

func (s *lessonListService) addDataSourceComment(ctx *gin.Context, studentList []map[string]interface{}, source map[string]struLessonList.LessonListSource) []map[string]interface{} {
	// 加入 debug_model
	if components.IsDebugModel(ctx) {
		for i, _ := range studentList {
			for key, info := range source {
				str, _ := json.Marshal(info)
				studentList[i][key+"_comment_deskcrm"] = string(str)
			}
		}
	}
	return studentList
}
