package lessonList

import (
	"deskcrm/service/arkBase/gray"
	"deskcrm/service/arkBase/lessonDataFactory"
	"deskcrm/service/arkBase/stable"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"reflect"
	"sort"
)

func (s *lessonListService) resetLessonList(ctx *gin.Context, lessonIDs []int64) (err error) {
	zlog.Infof(ctx, "resetLessonList lessonIds:%+v", lessonIDs)

	ids := make([]int64, 0)
	for _, lessonID := range lessonIDs {
		if _, ok := s.lessonMap[lessonID]; ok {
			ids = append(ids, lessonID)
		}
	}
	s.lock.Lock()
	s.lessonIds = ids
	s.lock.Unlock()
	return
}

// CheckRule 灰度、降级检查
func (s *lessonListService) CheckRule(ctx *gin.Context) (err error) {
	grayServ := gray.NewClient(ctx)
	for key, rule := range s.fieldRuleMap {
		//灰度检查
		isGray, _ := grayServ.IsGray(ctx, rule.Key, s.param.CourseId, s.param.PersonUid)
		if isGray == false { // true正常展示规则、 false剔除规则
			//zlog.Infof(ctx, "方舟规则命中灰度, ruleKey:%s, tplID:%d, serviceID:%d, AssistantUid:%d", rule.ID, rule.Key, s.param.AssistantUid)
			delete(s.fieldRuleMap, key)
		}
	}

	//批量检查所有字段是否命中降级,返回降级的规则key
	isFusingList, err := stable.FusingService.BatchCheckFusing(ctx, s.fieldRuleMap)
	if err != nil {
		return
	}
	for _, key := range isFusingList {
		//zlog.Infof(ctx, "方舟规则命中降级, ruleKey:%s, tplID:%d, serviceID:%d, AssistantUid:%d", key, s.param.AssistantUid)
		delete(s.fieldRuleMap, key)
	}
	return
}

// 获取format工厂需要参数
func (s *lessonListService) getLessonDataFactoryData(ctx *gin.Context, ruleKeys []string, studentUids []int64) (data *struLessonList.FormatFactoryData, err error) {
	zlog.Infof(ctx, "getLessonDataFactoryData, ruleKeys:%+v, lessonIds:%+v", ruleKeys, studentUids)

	return &struLessonList.FormatFactoryData{
		AssistantUid: s.param.AssistantUid,
		PersonUid:    s.param.PersonUid,
		CourseId:     s.param.CourseId,
		LessonIds:    s.lessonIds,
		LeadId:       s.param.LeadsId,
		StudentUid:   s.param.StudentUid,
		RuleKeys:     ruleKeys,
		RuleMap:      s.fieldRuleMap,
		GroupKey:     s.GroupKey,
	}, nil
}

func (s *lessonListService) outputToSliceAndSort(ctx *gin.Context, output *struLessonList.LessonListOutput) (lessonList []map[string]interface{}, err error) {
	zlog.Debugf(ctx, "outputToSliceAndSort, output:%+v", output)

	//排序
	lessonList = make([]map[string]interface{}, 0)
	for _, lessonInfo := range output.LessonListOutput {
		lessonList = append(lessonList, lessonInfo)
	}

	sort.Slice(lessonList, func(i, j int) bool {
		for _, sortRule := range s.sorts {
			sortKey := sortRule.Key
			iVal := lessonList[i][sortKey]
			jVal := lessonList[j][sortKey]

			// nil 值在比较中默认放在后面
			if iVal == nil {
				return false
			}

			if jVal == nil {
				return true
			}

			if fwyyutils.IsNumber(iVal) {
				iValFloat := cast.ToFloat64(iVal)
				jValFloat := cast.ToFloat64(jVal)
				if iValFloat == jValFloat {
					continue
				}
				if sortRule.Sort == "desc" {
					return iValFloat > jValFloat
				}
				return iValFloat < jValFloat
			} else if reflect.TypeOf(iVal).Kind() == reflect.String {
				iValString, _ := iVal.(string)
				jValString, _ := jVal.(string)
				if iValString == jValString {
					continue
				}
				if sortRule.Sort == "desc" {
					return iValString > jValString
				}
				return iValString < jValString
			}
		}
		return true
	})
	return lessonList, nil
}

// dataQueryWarming dataquery 字段预热
func (s *lessonListService) dataQueryWarming(ctx *gin.Context, formatFactoryData *struLessonList.FormatFactoryData) (err error) {
	_ = s.dataQueryPoint.SetStatusBefore(ctx)
	_ = lessonDataFactory.FormatRouter.Exec(ctx, s.dataQueryPoint, formatFactoryData, &struLessonList.LessonListOutput{
		LessonListSource: map[string]struLessonList.LessonListSource{},
		LessonListOutput: map[int64]map[string]interface{}{},
	})
	_ = s.dataQueryPoint.SetStatusExec(ctx)
	return
}

func (s *lessonListService) GetLessonIDs(ctx *gin.Context) []int64 {
	return s.lessonIds
}
