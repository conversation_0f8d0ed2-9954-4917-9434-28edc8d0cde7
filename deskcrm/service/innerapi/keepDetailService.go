package innerapi

import (
	"deskcrm/components"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/controllers/http/innerapi/output/outputKeepDetail"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/innerapi/keepDetail/lessonList"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var KeepDeatilService keepDetailService

type keepDetailService struct {
}

func (s keepDetailService) LessonList(ctx *gin.Context, param *inputKeepDetail.LessonListParam) (resp *outputKeepDetail.LessonOutput, err error) {
	resp = &outputKeepDetail.LessonOutput{}

	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "ArkList error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "ArkList panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	dataQueryPoint := dataQuery.New()
	leesonListPoint := lessonList.NewLessonList()
	err = leesonListPoint.InitDataQueryPoint(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitParam(ctx, param)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonList(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	if len(leesonListPoint.GetLessonIDs(ctx)) == 0 {
		return
	}

	err = leesonListPoint.InitLessonListConfig(ctx, false)
	if err != nil {
		return
	}

	//灰度、降级检查
	_ = leesonListPoint.CheckRule(ctx)

	var lessonDataList *lessonList.LessonListData
	lessonDataList, err = leesonListPoint.GetLessonList(ctx)
	if err != nil {
		return
	}

	resp = &outputKeepDetail.LessonOutput{
		LessonList: lessonDataList.LessonList,
		Total:      lessonDataList.Total,
	}
	return
}
