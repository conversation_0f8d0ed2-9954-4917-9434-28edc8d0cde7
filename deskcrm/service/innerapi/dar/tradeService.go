package dar

import (
	"deskcrm/api/zbcore"
	"deskcrm/consts"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// TradeInfo 交易信息结构体
type TradeInfo struct {
	StudentUid          int64 `json:"studentUid"`
	CourseId            int64 `json:"courseId"`
	TradeId             int64 `json:"tradeId"`
	SubTradeId          int64 `json:"subTradeId"`
	TradeFee            int64 `json:"tradeFee"`
	TradeTime           int64 `json:"tradeTime"`
	Status              int   `json:"status"`
	OrderBusinessStatus int   `json:"orderBusinessStatus"`
	RefundStatus        int   `json:"refundStatus"`
	RefundStartTime     int64 `json:"refundStartTime"`
	ChangeFromCourseId  int64 `json:"changeFromCourseId"`
	ChangeToCourseId    int64 `json:"changeToCourseId"`
	ChangeTime          int64 `json:"changeTime"`
	CreateTime          int64 `json:"createTime"`
	UpdateTime          int64 `json:"updateTime"`
}

// GetTradeKVByCourseIds 根据学生课程ID获取交易信息
// 对应 PHP 的 Fudao_Dar::getTradeKVByCourseIds
func GetTradeKVByCourseIds(ctx *gin.Context, studentCourseIds []string) (map[string]*TradeInfo, error) {
	if len(studentCourseIds) == 0 {
		return make(map[string]*TradeInfo), nil
	}

	// 调用 zbcore DAR 服务
	fields := []string{
		"userId", "courseId", "tradeId", "subTradeId", "tradeFee", "tradeTime",
		"orderBusinessStatus", "refundStatus", "refundStartTime", "changeFromCourseId",
		"changeToCourseId", "changeTime", "createTime", "updateTime", "logInfo",
	}

	input := map[string]interface{}{
		"studentCourseIds": studentCourseIds,
		"fields":           fields,
	}

	output := map[string]interface{}{}

	// 构建请求头
	headers := zbcore.GetHeaders("/dar/api/api/getKVByCourseIds", "dar", "trade", "getKVByCourseIds", true, "deskcrm")

	// 调用 DAR 服务
	resp, err := zbcore.PostDar(ctx, input, headers, &output)
	if err != nil {
		zlog.Errorf(ctx, "GetTradeKVByCourseIds call failed, err: %v", err)
		return nil, err
	}

	if resp == nil || resp.ErrNo != zbcore.Success {
		zlog.Errorf(ctx, "GetTradeKVByCourseIds api failed, errNo: %d, errMsg: %s", resp.ErrNo, resp.ErrMsg)
		return nil, fmt.Errorf("api failed: %s", resp.ErrMsg)
	}

	// 解析响应数据
	return parseTradeResponse(ctx, output)
}

// parseTradeResponse 解析交易响应数据
func parseTradeResponse(ctx *gin.Context, output map[string]interface{}) (map[string]*TradeInfo, error) {
	result := make(map[string]*TradeInfo)

	data, ok := output["data"].(map[string]interface{})
	if !ok {
		zlog.Warnf(ctx, "parseTradeResponse: data field not found or not map type")
		return result, nil
	}

	for key, value := range data {
		tradeData, ok := value.(map[string]interface{})
		if !ok {
			zlog.Warnf(ctx, "parseTradeResponse: tradeData for key %s is not map type", key)
			continue
		}

		// 计算交易状态
		orderBusinessStatus := cast.ToInt(tradeData["orderBusinessStatus"])
		refundStatus := cast.ToInt(tradeData["refundStatus"])
		status := consts.GetTradeStatus(orderBusinessStatus, refundStatus)

		// 过滤未知状态
		if status == consts.TradeStatusUnknown {
			zlog.Infof(ctx, "parseTradeResponse: skip unknown status trade, key: %s", key)
			continue
		}

		tradeInfo := &TradeInfo{
			StudentUid:          cast.ToInt64(tradeData["userId"]),
			CourseId:            cast.ToInt64(tradeData["courseId"]),
			TradeId:             cast.ToInt64(tradeData["tradeId"]),
			SubTradeId:          cast.ToInt64(tradeData["subTradeId"]),
			TradeFee:            cast.ToInt64(tradeData["tradeFee"]),
			TradeTime:           cast.ToInt64(tradeData["tradeTime"]),
			Status:              status,
			OrderBusinessStatus: orderBusinessStatus,
			RefundStatus:        refundStatus,
			RefundStartTime:     cast.ToInt64(tradeData["refundStartTime"]),
			ChangeFromCourseId:  cast.ToInt64(tradeData["changeFromCourseId"]),
			ChangeToCourseId:    cast.ToInt64(tradeData["changeToCourseId"]),
			ChangeTime:          cast.ToInt64(tradeData["changeTime"]),
			CreateTime:          cast.ToInt64(tradeData["createTime"]),
			UpdateTime:          cast.ToInt64(tradeData["updateTime"]),
		}

		result[key] = tradeInfo
	}

	zlog.Infof(ctx, "parseTradeResponse: parsed %d trade records", len(result))
	return result, nil
}

// GetPaidTradeKVByCourseIds 获取已支付未退款的交易信息
// 对应 PHP 的 Fudao_Dar::getPaidTradeKVByCourseIds
func GetPaidTradeKVByCourseIds(ctx *gin.Context, studentCourseIds []string) (map[string]*TradeInfo, error) {
	allTrades, err := GetTradeKVByCourseIds(ctx, studentCourseIds)
	if err != nil {
		return nil, err
	}

	// 过滤出已支付未退款的交易
	result := make(map[string]*TradeInfo)
	for key, trade := range allTrades {
		if trade.Status == consts.TradeStatusPaid {
			result[key] = trade
		}
	}

	zlog.Infof(ctx, "GetPaidTradeKVByCourseIds: filtered %d paid trades from %d total trades", len(result), len(allTrades))
	return result, nil
}

// GetTradeKVByTradeIds 根据交易ID获取交易信息
// 对应 PHP 的 Fudao_Dar::getTradeKVByTradeIds
func GetTradeKVByTradeIds(ctx *gin.Context, userTradeIds []string) (map[string]*TradeInfo, error) {
	if len(userTradeIds) == 0 {
		return make(map[string]*TradeInfo), nil
	}

	tradeFields := []string{"tradeId", "userId", "orderBusinessStatus", "saleChannel", "tradeTime", "createTime"}
	subTradeFields := []string{"subTradeId", "skuId", "courseId", "price", "tradeFee", "refundFee", "orderBusinessStatus", "refundStatus", "entityType"}

	input := map[string]interface{}{
		"userTradeIds":   userTradeIds,
		"tradeFields":    tradeFields,
		"subTradeFields": subTradeFields,
	}

	output := map[string]interface{}{}

	// 构建请求头
	headers := zbcore.GetHeaders("/dar/api/api/getKVByTradeIds", "dar", "trade", "getKVByTradeIds", true, "deskcrm")

	// 调用 DAR 服务
	resp, err := zbcore.PostDar(ctx, input, headers, &output)
	if err != nil {
		zlog.Errorf(ctx, "GetTradeKVByTradeIds call failed, err: %v", err)
		return nil, err
	}

	if resp == nil || resp.ErrNo != zbcore.Success {
		zlog.Errorf(ctx, "GetTradeKVByTradeIds api failed, errNo: %d, errMsg: %s", resp.ErrNo, resp.ErrMsg)
		return nil, fmt.Errorf("api failed: %s", resp.ErrMsg)
	}

	// 解析响应数据 (这里需要根据实际API响应格式调整)
	return parseTradeResponse(ctx, output)
}
