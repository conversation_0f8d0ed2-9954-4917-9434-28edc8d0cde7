package lessonDataFactory

import (
	"deskcrm/components"
	"deskcrm/helpers"
	"deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc"
	"deskcrm/service/arkBase/stable"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"reflect"
	"sync"
	"time"
)

const FuncParamNum = 1
const FuncOutputNum = 1

const DataSourceFuncOutputNum = 2

const maxThread = 50 //方舟方法单次最大并发量

var FormatFactoryBase formatFactoryBase

type formatFactoryBase struct {
}

func (s *formatFactoryBase) cost() func(ctx *gin.Context, ruleKey string, fusingRule struLessonList.FusingRuleStru) {
	beginTime := time.Now()
	return func(ctx *gin.Context, ruleKey string, fusingRule struLessonList.FusingRuleStru) {
		cost := time.Since(beginTime).Milliseconds()
		if cost > fusingRule.TimeoutWarning { //撞报警墙，只报警
			//todo 线上要配置配套采集、报警
			zlog.Warnf(ctx, "(此条为预警阈值，不是熔断阈值不会触发熔断) 触发熔断预警阈值，请相关研发关注， key:%s", ruleKey)
		}

		if cost > fusingRule.TimeoutFusing { //撞熔断墙，记录次数
			//todo 线上要配置配套采集、报警
			zlog.Warnf(ctx, "熔断预警， key:%s", ruleKey)
			redisKey := components.Redis.GetFusingTimeLimitKey(ctx, ruleKey)

			_, err := helpers.RedisClient.Incr(ctx, redisKey)
			if err != nil {
				zlog.Errorf(ctx, "fusing count fail, key:%s", ruleKey)
				return
			}
			//时间段不严格按照滑动窗口式统计数量，这里用栈记录每次撞墙时间逻辑较重
			//这里直接用时间桶来装数量，降级的场景核心还是能快速发现并触发降级
			if code, _ := helpers.RedisClient.Ttl(ctx, redisKey); code == -1 {
				_, _ = helpers.RedisClient.Expire(ctx, redisKey, int64(fusingRule.Duration))
			}
		}
	}
}

func (s *formatFactoryBase) call(ctx *gin.Context, formatPoint *lessonDataFunc.Format) (err error) {
	defer func() {
		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "ark service, formatFactoryBase call panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	rule, _ := formatPoint.GetRule(ctx)
	defer FormatFactoryBase.cost()(ctx, rule.Key, stable.FusingService.GetFusingConfig(ctx, rule.Key, rule.FusingRule)) //规则耗时采集,规则熔断依赖次数据
	funcName, _ := formatPoint.GetFuncName(ctx)
	refPoint := reflect.ValueOf(formatPoint).MethodByName(funcName)
	if refPoint.Kind() != reflect.Func {
		zlog.Warnf(ctx, "format router ark func not found:%s", funcName)
		return
	}

	//入参必须是1个
	if refPoint.Type().NumIn() != FuncParamNum {
		zlog.Warnf(ctx, "func param num error, funcName:%s, FuncParamNum:%d", funcName, refPoint.Type().NumIn())
		return
	}

	//返回值只允许有一个，且必须为error
	if refPoint.Type().NumOut() != FuncOutputNum {
		zlog.Warnf(ctx, "func return num error, funcName:%s, FuncOutputNum:%d", funcName, refPoint.Type().NumOut())
		return
	}

	outs := make([]reflect.Type, 0, refPoint.Type().NumOut())
	for i := 0; i < refPoint.Type().NumOut(); i++ {
		arg := refPoint.Type().Out(i)
		outs = append(outs, arg)
	}

	//检查返回的最后一个参数是否是error
	if !outs[len(outs)-1].AssignableTo(reflect.TypeOf((*error)(nil)).Elem()) {
		zlog.Warnf(ctx, "func last return error, funcName:%s", funcName)
		return
	}

	callParam := []reflect.Value{
		reflect.ValueOf(ctx),
	}
	resultValues := refPoint.Call(callParam)
	for i := range resultValues {
		if resultValues[i].Interface() != nil {
			err = resultValues[i].Interface().(error)
			zlog.Errorf(ctx, "func return error, funcName:%s, err:%+v", funcName, err.Error())
		}
	}

	return
}

func (s *formatFactoryBase) chunkArrayArkRule(ruleList []*struLessonList.LessonRuleConfigStru, size int) (result [][]*struLessonList.LessonRuleConfigStru) {
	if len(ruleList) == 0 || size <= 0 {
		return nil
	}

	total := len(ruleList) / size
	if len(ruleList)%size != 0 {
		total++
	}

	result = make([][]*struLessonList.LessonRuleConfigStru, total)
	chunkNum := 0
	cursor := 0
	for _, rule := range ruleList {
		if cursor == 0 {
			result[chunkNum] = make([]*struLessonList.LessonRuleConfigStru, 0)
		}

		result[chunkNum] = append(result[chunkNum], rule)

		if cursor == size {
			cursor = 0
			chunkNum++
		} else {
			cursor++
		}
	}
	return result
}

func (s *formatFactoryBase) initOutput(ctx *gin.Context, lesssonIDs []int64) (output *struLessonList.LessonListOutput) {
	output = &struLessonList.LessonListOutput{
		LessonListSource: map[string]struLessonList.LessonListSource{},
		LessonListOutput: map[int64]map[string]interface{}{},
	}

	for _, lessonID := range lesssonIDs {
		output.LessonListOutput[lessonID] = map[string]interface{}{
			"lessonId": lessonID,
		}
	}
	return
}

func (s *formatFactoryBase) mergeOutput(ctx *gin.Context, one *struLessonList.LessonListOutput, two *struLessonList.LessonListOutput, lock *sync.Mutex) *struLessonList.LessonListOutput {
	if one == nil {
		return two
	}
	if two == nil {
		return one
	}
	lock.Lock()
	for idx, val := range two.LessonListSource {
		one.LessonListSource[idx] = val
	}

	for studentUid, studentFields := range two.LessonListOutput {
		if _, ok := one.LessonListOutput[studentUid]; !ok {
			one.LessonListOutput[studentUid] = map[string]interface{}{}
		}
		for field, value := range studentFields {
			one.LessonListOutput[studentUid][field] = value
		}
	}
	lock.Unlock()
	return one
}
