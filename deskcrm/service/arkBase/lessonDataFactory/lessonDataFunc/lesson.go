package lessonDataFunc

import (
	"deskcrm/api/dal"
	"deskcrm/service/arkBase/dataQuery"
	"github.com/gin-gonic/gin"
	"time"
)

func (s *Format) GetLessonName(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, leesonDetail := range courseInfo.LessonList {
		lessonMap[int64(leesonDetail.LessonId)] = leesonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		lessonName := ""
		if _, ok := lessonMap[lessonID]; ok {
			lessonName = lessonMap[lessonID].LessonName
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonName)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}

func (s *Format) GetLessonStartDate(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceApiLessonReport, []string{}) {
		return
	}
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseInfo := queryData.(dal.CourseInfo)
	lessonMap := map[int64]dal.LessonInfo{}
	for _, leesonDetail := range courseInfo.LessonList {
		lessonMap[int64(leesonDetail.LessonId)] = leesonDetail
	}

	for _, lessonID := range s.param.LessonIDs {
		startDate := ""
		if _, ok := lessonMap[lessonID]; ok {
			startTime := lessonMap[lessonID].StartTime
			startDate = time.Unix(int64(startTime), 0).Format("2006-01-02 15:04")
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, startDate)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}
