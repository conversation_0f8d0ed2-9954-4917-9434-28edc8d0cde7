package lessonDataFunc

import (
	"deskcrm/service/arkBase/dataQuery"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

type Format struct {
	ctx            *gin.Context
	funcName       string
	param          LessonDataParam
	rule           *LessonDataRule
	dataQueryPoint *dataQuery.Singleton
	lessonMap      map[int64]struct{}
	lock           *sync.Mutex
	output         *struLessonList.LessonListOutput
}

type LessonDataParam struct {
	AssistantUid int64
	PersonUid    int64
	CourseID     int64
	LessonIDs    []int64
	StudentUid   int64
	LeadsId      int64
	GroupKey     string
}

type LessonDataRule struct {
	Name       string
	Key        string
	FusingRule struLessonList.FusingRuleStru
}

func NewFormat(ctx *gin.Context) *Format {
	c := &Format{
		ctx:       ctx,
		lessonMap: map[int64]struct{}{},
	}
	return c
}

func (s *Format) SetParam(ctx *gin.Context, formatParam LessonDataParam) (err error) {
	s.param = formatParam
	for _, lessonID := range s.param.LessonIDs {
		s.lessonMap[lessonID] = struct{}{}
	}
	return
}

func (s *Format) SetDataQueryPoint(ctx *gin.Context, dataQueryPoint *dataQuery.Singleton) (err error) {
	s.dataQueryPoint = dataQueryPoint
	return
}

func (s *Format) SetLock(ctx *gin.Context, lock *sync.Mutex) (err error) {
	s.lock = lock
	return
}

func (s *Format) SetOutput(ctx *gin.Context, output *struLessonList.LessonListOutput) (err error) {
	s.output = output
	return
}

func (s *Format) SetRule(ctx *gin.Context, rule *LessonDataRule) (err error) {
	s.rule = rule
	return
}

func (s *Format) SetFuncName(ctx *gin.Context, funcName string) (err error) {
	s.funcName = funcName
	return
}

func (s *Format) GetFuncName(ctx *gin.Context) (funcName string, err error) {
	return s.funcName, nil
}

func (s *Format) GetRule(ctx *gin.Context) (rule *LessonDataRule, err error) {
	return s.rule, nil
}

func (s *Format) AddOutputStudent(ctx *gin.Context, lessonID int64, key string, value interface{}) (err error) {
	if _, ok := s.lessonMap[lessonID]; !ok {
		zlog.Errorf(ctx, "[警告-异常数据], 非法插入的方舟数据，非任务列表例子的数据尝试写入任务列表! lessonID:%d", lessonID)
		return
	}

	s.lock.Lock()
	if _, ok := s.output.LessonListOutput[lessonID]; !ok {
		s.output.LessonListOutput[lessonID] = map[string]interface{}{}
	}
	s.output.LessonListOutput[lessonID][key] = value
	s.lock.Unlock()
	return
}

func (s *Format) AddDataSource(ctx *gin.Context, key string, title, dataSource string) (err error) {
	s.lock.Lock()
	s.output.LessonListSource[key] = struLessonList.LessonListSource{Key: key, Title: title, DataSource: dataSource}
	s.lock.Unlock()
	return
}
