package lessonDataFactory

import (
	"deskcrm/components"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

var FormatRouter formatRouter

type formatRouter struct {
}

func (s *formatRouter) Exec(ctx *gin.Context, dataQueryPoint *dataQuery.Singleton, formatFactoryData *struLessonList.FormatFactoryData, output *struLessonList.LessonListOutput) (err error) {
	lock := &sync.Mutex{}
	defer func() {
		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "ark service, format exec panic , err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	if len(formatFactoryData.LessonIds) == 0 {
		return nil
	}

	standardRuleList := make([]*struLessonList.LessonRuleConfigStru, 0)

	//规则数据分类
	for _, key := range formatFactoryData.RuleKeys {
		ruleDetail, ok := formatFactoryData.RuleMap[key]
		if !ok {
			continue
		}
		if len(ruleDetail.Function) > 0 { //字段列
			standardRuleList = append(standardRuleList, ruleDetail)
		}
	}

	formatParam, err := s.getFormatParam(ctx, formatFactoryData)
	if err != nil {
		return
	}

	standardOutput := &struLessonList.LessonListOutput{}

	standardOutput, _ = StandardRouter.Exec(ctx, dataQueryPoint, formatParam, standardRuleList, lock)

	if dataQueryPoint.IsExec(ctx) {
		output = FormatFactoryBase.mergeOutput(ctx, output, standardOutput, lock)
	}
	return
}

// 组装字段列format模型入参
func (s *formatRouter) getFormatParam(ctx *gin.Context, formatFactoryData *struLessonList.FormatFactoryData) (param lessonDataFunc.LessonDataParam, err error) {
	param = lessonDataFunc.LessonDataParam{
		AssistantUid: formatFactoryData.AssistantUid,
		PersonUid:    formatFactoryData.PersonUid,
		CourseID:     formatFactoryData.CourseId,
		LessonIDs:    formatFactoryData.LessonIds,
		StudentUid:   formatFactoryData.StudentUid,
		LeadsId:      formatFactoryData.LeadId,
		GroupKey:     formatFactoryData.GroupKey,
	}
	return param, nil
}
