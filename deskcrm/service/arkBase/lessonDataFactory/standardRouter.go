package lessonDataFactory

import (
	"deskcrm/components"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sync"
)

var StandardRouter standardRouter

type standardRouter struct {
}

func (s *standardRouter) Exec(ctx *gin.Context, dataQueryPoint *dataQuery.Singleton, param lessonDataFunc.LessonDataParam, ruleList []*struLessonList.LessonRuleConfigStru, lock *sync.Mutex) (output *struLessonList.LessonListOutput, err error) {
	var wg sync.WaitGroup
	output = FormatFactoryBase.initOutput(ctx, param.LessonIDs)
	formatErrs := make([]error, 0)
	arkRuleChunk := FormatFactoryBase.chunkArrayArkRule(ruleList, maxThread)
	for _, arkRuleList := range arkRuleChunk {
		for _, ruleDetail := range arkRuleList {
			formatPoint := lessonDataFunc.NewFormat(ctx)
			_ = formatPoint.SetParam(ctx, param)
			_ = formatPoint.SetDataQueryPoint(ctx, dataQueryPoint)
			_ = formatPoint.SetLock(ctx, lock)
			_ = formatPoint.SetRule(ctx, &lessonDataFunc.LessonDataRule{
				Name:       ruleDetail.Lable,
				Key:        ruleDetail.Key,
				FusingRule: ruleDetail.FusingRule,
			})
			_ = formatPoint.SetOutput(ctx, output)
			_ = formatPoint.SetFuncName(ctx, components.Util.FirstToUpper(ruleDetail.Function))

			wg.Add(1)
			go func() {
				defer wg.Done()
				formatErr := FormatFactoryBase.call(ctx, formatPoint)
				if formatErr != nil {
					formatErrs = append(formatErrs, formatErr)
				}
			}()
		}

		wg.Wait()
	}

	if len(formatErrs) > 0 {
		// 一个字段失败不适合全部失败
		zlog.Warnf(ctx, "router exec err, studentList format失败, errList: %+v", formatErrs)
	}
	return
}
