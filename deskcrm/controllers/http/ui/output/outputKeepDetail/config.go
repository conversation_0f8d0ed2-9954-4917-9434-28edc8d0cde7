package outputKeepDetail

type GetSchemaByCourseIdResp struct {
	Schema string `json:"schema"`
}

type GetContactFlagOptionResp struct {
	ContactFlagOption map[string]string `json:"contactFlagOption"`
}

type GetCallTypeListResp struct {
	CallTypeList []CallTypeItem `json:"callTypeList"`
}

type CallTypeItem struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}

type CommonGrayConfig struct {
	IsCommon         int `json:"isCommon"`
	IsCommonDetail   int `json:"isCommonDetail"`
	LpcOldPersonUids int `json:"lpcOldPersonUids"`
	DetailConfigGray int `json:"detailConfigGray"`
}
