package outputStudent

type StudentDelaminationDifferenceListV1Resp struct {
	HasDelamination int                      `json:"hasDelamination"`
	Total           int                      `json:"total"`
	PageSize        int                      `json:"pageSize"`
	TotalPage       int                      `json:"totalPage"`
	Page            int                      `json:"page"`
	DiffrenceList   []DelaminationDifference `json:"diffrenceList"` // 注意这里是拼写错误，应该是"differenceList"

}

// DelaminationDifference 分层差异详情
type DelaminationDifference struct {
	UserBedType      string           `json:"userBedType"`
	UserBedTypeHover string           `json:"userBedTypeHover"`
	Model            string           `json:"model"`
	ModelHover       string           `json:"modelHover"`
	Class            string           `json:"class"`
	SaveTime         string           `json:"saveTime"`
	UserBedRank      string           `json:"userBedRank"`
	Detail           []CategoryDetail `json:"detail"`
}

// CategoryDetail 分类详情
type CategoryDetail struct {
	ClassName string      `json:"className"`
	List      []Indicator `json:"list"`
}

// Indicator 指标数据
type Indicator struct {
	Value     interface{} `json:"value"` // 使用interface{}因为值可能是string或int
	Label     interface{} `json:"label"`
	Highlight int         `json:"highlight"`
}
