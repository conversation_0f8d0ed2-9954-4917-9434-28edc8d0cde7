package outputStudent

// InterviewReferLpcOutput 家访参考信息响应结构
type InterviewReferLpcOutput struct {
	HasLpcCourse     int                `json:"hasLpcCourse"`     // 是否有LPC课程 0-无 1-有
	HasQuestionnaire int                `json:"hasQuestionnaire"` // 是否有挖需问卷 0-无 1-有
	HasRegistTest    int                `json:"hasRegistTest"`    // 是否有摸底测 0-无 1-有
	LpcRemarks       []ReferRemarkInfo  `json:"lpcRemarks"`       // 备注信息
	Questionnaire    *QuestionnaireInfo `json:"questionnaire"`    // 挖需问卷信息
	RegistTest       *RegistTestInfo    `json:"registTest"`       // 摸底测信息
}

// ReferCourseInfo 转化课程信息
type ReferCourseInfo struct {
	CourseId   int64  `json:"courseId"`   // LPC课程ID
	LpcUid     int64  `json:"lpcUid"`     // LPC老师UID
	CourseName string `json:"courseName"` // 课程名称
}

// RegistTestInfo 摸底测信息
type RegistTestInfo struct {
	IsFinish   int    `json:"isFinish"`   // 是否完成 0-未完成 1-已完成
	CostTime   string `json:"costTime"`   // 耗时
	Score      int    `json:"score"`      // 分数
	FinishTime string `json:"finishTime"` // 完成时间
	CourseId   int64  `json:"courseId"`   // LPC课程ID
	CourseName string `json:"courseName"` // 课程名称
	StudentUid int64  `json:"studentUid"` // 学生UID
	Department int    `json:"department"` // 学段信息
}

// QuestionnaireInfo 挖需问卷信息
type QuestionnaireInfo struct {
	IsFinish   int      `json:"isFinish"`   // 是否完成 0-未完成 1-已完成
	Labels     []string `json:"labels"`     // 标签列表
	DetailUrl  string   `json:"detailUrl"`  // 详情URL
	FinishTime string   `json:"finishTime"` // 完成时间
	IsBind     int      `json:"isBind"`     // 是否绑定 0-未绑定 1-已绑定
	CourseName string   `json:"courseName"` // 课程名称（PHP版本添加的字段）
}

// ReferRemarkInfo 备注信息
type ReferRemarkInfo struct {
	RemarkTime string `json:"remarkTime"` // 备注时间
	Remark     string `json:"remark"`     // 备注内容
	CourseName string `json:"courseName"` // 课程名称
}

// InterviewRecordOutput 访谈记录响应结构
type InterviewRecordOutput struct {
	InterviewRecord []InterviewRecordItem `json:"interviewRecord"` // 访谈记录列表
	SaleCallRecord  []any                 `json:"saleCallRecord"`  // 销售维系记录列表
}

// MergedRecord 合并记录结构体（支持手动和AI记录，字段平铺）
type MergedRecord struct {
	RecordType int `json:"recordType"` // 记录类型：1=手动记录, 2=AI记录

	// 手动记录字段
	ID            *int64  `json:"id,omitempty"`            // 记录ID
	InterviewTime *string `json:"interviewTime,omitempty"` // 访谈时间
	Content       *string `json:"content,omitempty"`       // 内容
	CourseName    *string `json:"courseName,omitempty"`    // 课程名称
	Interviewer   *string `json:"interviewer,omitempty"`   // 访谈人
	ChannelType   *int    `json:"channelType,omitempty"`   // 渠道类型
	CanEdit       *int    `json:"canEdit,omitempty"`       // 是否可编辑
	Friendliness  *int    `json:"friendliness,omitempty"`  // 亲密度
	RoleType      *int    `json:"roleType,omitempty"`      // 角色类型
	PhaseId       *int    `json:"phaseId,omitempty"`       // 阶段ID
	PhaseKey      *int    `json:"phaseKey,omitempty"`      // 阶段Key
	InterviewType *[]int  `json:"interviewType,omitempty"` // 访谈类型

	// AI记录字段
	CallId      *string `json:"callId,omitempty"`      // 通话ID
	StartTime   *int64  `json:"startTime,omitempty"`   // 开始时间戳
	StopTime    *int64  `json:"stopTime,omitempty"`    // 结束时间戳
	Status      *int    `json:"status,omitempty"`      // 状态
	Type        *int    `json:"type,omitempty"`        // 类型
	RecordFile  *string `json:"recordFile,omitempty"`  // 录音文件URL
	SourceType  *string `json:"sourceType,omitempty"`  // 来源类型
	CourseId    *int64  `json:"courseId,omitempty"`    // 课程ID
	CallMode    *int    `json:"callMode,omitempty"`    // 通话模式
	CallModeStr *string `json:"callModeStr,omitempty"` // 通话模式字符串
	Duration    *int64  `json:"duration,omitempty"`    // 通话时长
	Abstract    *string `json:"abstract,omitempty"`    // 摘要
	Accurate    *int    `json:"accurate,omitempty"`    // 准确度
	Inaccurate  *int    `json:"inaccurate,omitempty"`  // 不准确度
	CurrentTime *int64  `json:"currentTime,omitempty"` // 当前时间戳
	Version     *int    `json:"version,omitempty"`     // 版本

	// AI记录中缺失的字段（从AICallDetail补充）
	ContentArray *any `json:"contentArray,omitempty"` // 通话内容数组（复杂结构，使用any）
	Tags         *any `json:"tags,omitempty"`         // 标签数组（复杂结构，使用any）

	// 通用字段
	CreateTime int64 `json:"createTime"` // 创建时间戳（用于排序）
}

// InterviewRecordV2Output 访谈记录V2响应结构
type InterviewRecordV2Output struct {
	InterviewRecord []MergedRecord `json:"interviewRecord"` // 合并后的访谈记录列表
	Page            int            `json:"page"`            // 当前页码
	PageSize        int            `json:"pageSize"`        // 每页大小
	Total           int            `json:"total"`           // 总记录数
}

// InterviewRecordItem 访谈记录项
type InterviewRecordItem struct {
	ID            int64  `json:"id"`            // 记录ID
	InterviewTime string `json:"interviewTime"` // 访谈时间
	Content       string `json:"content"`       // 内容
	CourseName    string `json:"courseName"`    // 课程名称
	Interviewer   string `json:"interviewer"`   // 访谈人
	ChannelType   int    `json:"channelType"`   // 渠道类型
	CanEdit       int    `json:"canEdit"`       // 是否可编辑 1=可编辑, 0=不可编辑
	Friendliness  int    `json:"friendliness"`  // 亲密度
	RoleType      int    `json:"roleType"`      // 角色类型
	PhaseId       int    `json:"phaseId"`       // 阶段ID
	PhaseKey      int    `json:"phaseKey"`      // 阶段Key
	InterviewType []int  `json:"interviewType"` // 访谈类型数组
	CreateTime    int64  `json:"createTime"`    // 创建时间戳
	RecordType    int    `json:"recordType"`    // 记录类型 1=手动, 2=AI
}
