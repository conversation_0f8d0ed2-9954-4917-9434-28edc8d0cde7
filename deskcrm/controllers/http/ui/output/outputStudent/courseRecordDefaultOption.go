package outputStudent

// CourseRecordDefaultOptionResp 获取课程记录默认选项响应
// 对应 PHP 接口的响应格式
type CourseRecordDefaultOptionResp struct {
	Success            bool                   `json:"success"`
	Data               map[string]interface{} `json:"data,omitempty"`
	Message            string                 `json:"message,omitempty"`
	Season             int64                  `json:"season"`             // 学季
	NewCourseType      int64                  `json:"newCourseType"`      // 新课程类型
	IsRefund           int                    `json:"isRefund"`           // 是否退款状态
	Year               int64                  `json:"year"`               // 年份
	CourseServiceType  int                    `json:"courseServiceType"`  // 课程服务类型
}

// NewCourseRecordDefaultOptionResp 创建获取课程记录默认选项响应
func NewCourseRecordDefaultOptionResp() *CourseRecordDefaultOptionResp {
	return &CourseRecordDefaultOptionResp{
		Success: true,
		Data:    make(map[string]interface{}),
	}
}

// SetData 设置数据字段
func (r *CourseRecordDefaultOptionResp) SetData(season, newCourseType, year int64, isRefund, courseServiceType int) {
	r.Season = season
	r.NewCourseType = newCourseType
	r.IsRefund = isRefund
	r.Year = year
	r.CourseServiceType = courseServiceType
	
	// 同时设置到Data字段中保持兼容性
	r.Data["season"] = season
	r.Data["newCourseType"] = newCourseType
	r.Data["isRefund"] = isRefund
	r.Data["year"] = year
	r.Data["courseServiceType"] = courseServiceType
}