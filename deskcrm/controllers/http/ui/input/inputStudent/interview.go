package inputStudent

import (
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/middleware"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

// InterviewReferLpcParam 家访参考信息请求参数
type InterviewReferLpcParam struct {
	CourseId   int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程id
	StudentUid int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生uid
}

func (p *InterviewReferLpcParam) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 || p.CourseId <= 0 {
		return components.ErrorParamInvalid
	}
	return nil
}

// InterviewRecordV2Param 访谈记录V2请求参数
type InterviewRecordV2Param struct {
	StudentUid    int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	CourseId      int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
	Type          int   `json:"type" form:"type" binding:"required"`             // 类型 consts.StudentInterviewViewAll=查看全部, consts.StudentInterviewViewSelf=只看自己
	InterviewType int   `json:"interviewType" form:"interviewType"`              // 记录类型 0=查看全部, consts.StudentInterviewRecordTypeManual=只看手动, consts.StudentInterviewRecordTypeAI=只看AI
	Page          int   `json:"page" form:"page" binding:"required"`             // 页码
	PageSize      int   `json:"pageSize" form:"pageSize" binding:"required"`     // 每页大小
	Origin        int   `json:"origin" form:"origin"`                            // 来源 consts.ServicePageDeskStudentInterviewOriginDesk=DESK, consts.ServicePageDeskStudentInterviewOriginApi=API
	AssistantUid  int64 `json:"assistantUid" form:"assistantUid"`                // 助教UID（当origin=API时使用）
}

// Validate 验证参数
func (p *InterviewRecordV2Param) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 || p.CourseId <= 0 || p.Type <= 0 || p.Page <= 0 || p.PageSize <= 0 {
		return components.ErrorParamInvalid
	}

	// 如果origin未设置，默认为DESK
	if p.Origin == 0 {
		p.Origin = consts.ServicePageDeskStudentInterviewOriginDesk
	}

	// 如果是DESK来源，从登录信息中获取助教UID
	if p.Origin == consts.ServicePageDeskStudentInterviewOriginDesk {
		loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
		if err != nil {
			base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
			return components.ErrorParamInvalid
		}
		p.AssistantUid = loginDeviceInfo.DeviceUid
	}

	return nil
}
