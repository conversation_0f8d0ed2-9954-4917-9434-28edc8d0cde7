package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

type StudentDelaminationDifferenceListV1Param struct {
	StudentUid   int64  `json:"studentUid" form:"studentUid" binding:"required"`
	CourseId     int64  `json:"courseId" form:"courseId" binding:"required"`
	PageSize     int    `json:"pageSize" form:"pageSize"`
	Page         int    `json:"page" form:"page"`
	Keys         string `json:"keys" form:"keys"`
	KeysArray    []string
	KeysMap      map[string]struct{}
	AssistantUid int64 `form:"assistantUid" json:"assistantUid"`
	PersonUid    int64 `form:"personUid" json:"personUid"`
}

func (c *StudentDelaminationDifferenceListV1Param) Validate(ctx *gin.Context) error {

	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail device error, err: %+v", err)
		return components.ErrorParamInvalid
	}

	c.AssistantUid = loginDeviceInfo.DeviceUid

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail uid error, err: %+v", err)
		return components.ErrorParamInvalid
	}

	c.PersonUid = int64(userInfo.UserId)

	if c.PageSize == 0 {
		c.PageSize = 1
	}

	if c.PageSize > 5 {
		c.PageSize = 5
	}

	if c.Page == 0 {
		c.Page = 1
	}

	keysMap := make(map[string]struct{})
	c.KeysArray = strings.Split(c.Keys, ",")

	for _, key := range c.KeysArray {
		keysMap[key] = struct{}{}
	}
	c.KeysMap = keysMap
	return nil
}
