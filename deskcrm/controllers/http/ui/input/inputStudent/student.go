package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"
	"deskcrm/service/arkBase/arkConfig"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// request参数
type StudentListParam struct {
	TplId           int64  `form:"tplId" json:"tplId"`
	CourseId        int64  `form:"courseId" json:"courseId"`
	LessonId        int64  `form:"lessonId" json:"lessonId"`
	ServiceId       int64  `form:"serviceId" json:"serviceId"`
	TaskId          int64  `form:"taskId" json:"taskId"`
	Timestamp       int64  `form:"timestamp" json:"timestamp"`
	Pn              int64  `form:"pn" json:"pn"`
	Rn              int64  `form:"rn" json:"rn"`
	Keyword         string `form:"keyword" json:"keyword"`
	Filter          string `form:"filter" json:"filter"`
	Sorts           string `form:"sorts" json:"sorts"`
	AssistantUid    int64  `form:"assistantUid" json:"assistantUid"`
	PersonUid       int64  `form:"personUid" json:"personUid"`
	DataRangeSelect string `form:"dataRangeSelect" json:"dataRangeSelect"`
}

func (c *StudentListParam) Validate(ctx *gin.Context) error {
	if c.TplId == 0 {
		bindDetail, err := arkConfig.GetTemplateConfigInstance(ctx).GetArkTemplateBindsByCourseId(ctx, int(c.CourseId))
		if err != nil {
			return err
		}
		c.TplId = bindDetail.TplId
	}

	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail device error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}

	c.AssistantUid = loginDeviceInfo.DeviceUid

	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Infof(ctx, "collection loginFail uid error, err: %+v", err)
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}

	c.PersonUid = int64(userInfo.UserId)
	return nil
}

func (c *StudentListParam) ValidateAPI(ctx *gin.Context) error {
	if c.TplId == 0 {
		bindDetail, err := arkConfig.GetTemplateConfigInstance(ctx).GetArkTemplateBindsByCourseId(ctx, int(c.CourseId))
		if err != nil {
			return err
		}
		c.TplId = bindDetail.TplId
	}

	return nil
}

// GetWxBindInfoParam 获取微信绑定信息请求参数
type GetWxBindInfoParam struct {
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`                // 辅导老师UID
	StudentUid   int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	CourseId     int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
}

// Validate 参数验证
func (p *GetWxBindInfoParam) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 || p.CourseId <= 0 {
		return components.ErrorParamInvalid
	}

	loginDeviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}
	p.AssistantUid = loginDeviceInfo.DeviceUid

	return nil
}
