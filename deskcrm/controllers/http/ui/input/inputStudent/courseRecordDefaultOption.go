package inputStudent

import (
	"deskcrm/components"
	"github.com/gin-gonic/gin"
)

// CourseRecordDefaultOptionParam 获取课程记录默认选项参数
type CourseRecordDefaultOptionParam struct {
	CourseId   int `form:"courseId" json:"courseId" binding:"required"`
	StudentUid int `form:"studentUid" json:"studentUid" binding:"required"`
}

// Validate 验证参数
func (p *CourseRecordDefaultOptionParam) Validate(ctx *gin.Context) error {
	if p.CourseId <= 0 {
		return components.InvalidParam("courseId不能为空")
	}
	if p.StudentUid <= 0 {
		return components.InvalidParam("studentUid不能为空")
	}
	return nil
}