package inputKeepDetail

import (
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

type LessonListParam struct {
	AssistantUid     int64  `json:"assistantUid" form:"assistantUid" binding:"required"`
	PersonUid        int64  `json:"personUid" form:"personUid" binding:"required"`
	CourseId         int64  `json:"courseId" form:"courseId" binding:"required"`
	StudentUid       int64  `json:"studentUid" form:"studentUid" binding:"required"`
	LeadsId          int64  `json:"leadsId" form:"leadsId" binding:"required"`
	Tab              string `json:"tab" form:"tab" binding:"required"`
	Sorts            string `json:"sorts" form:"sorts"`
	NeedFieldKeysOri string `json:"needFieldKeys" form:"needFieldKeys"`
	NeedFieldKeys    []string
	Offset           int64 `json:"offset" form:"offset"`
	Limit            int64 `json:"limit" form:"limit"`
}

func (c *LessonListParam) Validate(ctx *gin.Context) error {
	if len(c.<PERSON><PERSON>) > 0 {
		_ = jsoniter.UnmarshalFromString(c.<PERSON>eysOri, &c.NeedFieldKeys)
	}
	if c.Limit == 0 {
		c.Limit = 100
	}
	return nil
}
