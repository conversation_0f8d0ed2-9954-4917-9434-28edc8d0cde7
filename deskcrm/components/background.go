package components

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

const (
	AssistantUid = "assistantUid"
	PersonUid    = "personUid"
)

func SetAssistantUid(ctx *gin.Context, assistantUid int64) {
	if assistantUid > 0 {
		ctx.Set(AssistantUid, assistantUid)
	}
}

func SetPersonUid(ctx *gin.Context, personUid int64) {
	if personUid > 0 {
		ctx.Set(PersonUid, personUid)
	}
}

func GetAssistantUid(ctx *gin.Context) int64 {
	val := ctx.Value(AssistantUid)
	if val != nil {
		return cast.ToInt64(val)
	}
	return 0
}

func GetPersonUid(ctx *gin.Context) int64 {
	val := ctx.Value(PersonUid)
	if val != nil {
		return cast.ToInt64(val)
	}
	return 0
}
