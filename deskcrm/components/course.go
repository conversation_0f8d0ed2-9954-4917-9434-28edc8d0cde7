package components

// 课程类型
// 注意：公开课、家长课在业务上废弃已久，请不要使用
const (
	CourseTypePrivate           = 0  // 专题课
	CourseTypePrivateLong       = 2  // 班课
	CourseTypePreLong           = 4  // 试听课
	CourseTypePublic            = 1  // 公开课（已废弃）
	CourseTypeParentCourse      = 3  // 家长课（已废弃）
	CourseTypeBbChild           = 6  //帮帮英语低幼
	CourseTypeYyWriteClass      = 7  //鸭鸭写字正价课
	CourseTypeYyWriteExperience = 8  //鸭鸭写字体验课
	CourseTypeYyEnglishWeek     = 9  //鸭鸭英语周课
	CourseTypeYyEnglishTwoWeek  = 10 //鸭鸭英语双周课
	CourseTypeYyEnglishYear     = 11 //鸭鸭英语年课
	CourseTypeYyChineseWeek     = 14 //鸭鸭语文周课
	CourseTypeYyChineseTwoWeek  = 15 //鸭鸭语文双周课
	CourseTypeYyChineseMonth    = 16 //鸭鸭语文月课
	CourseTypeYyChineseHalfYear = 17 //鸭鸭语文半年课
	CourseTypeYyChineseYear     = 18 //鸭鸭语文年课
)

var courseTypeMapping = map[int]string{
	CourseTypePrivate:           "专题课",
	CourseTypePublic:            "公开课",
	CourseTypePrivateLong:       "班课",
	CourseTypeParentCourse:      "家长课",
	CourseTypePreLong:           "试听课",
	CourseTypeBbChild:           "帮帮英语低幼",
	CourseTypeYyWriteClass:      "鸭鸭写字系统课",
	CourseTypeYyWriteExperience: "鸭鸭写字体验课",
	CourseTypeYyEnglishWeek:     "鸭鸭英语周课",
	CourseTypeYyEnglishTwoWeek:  "鸭鸭英语双周课",
	CourseTypeYyEnglishYear:     "鸭鸭英语年课",
	CourseTypeYyChineseWeek:     "鸭鸭语文周课",
	CourseTypeYyChineseTwoWeek:  "鸭鸭语文双周课",
	CourseTypeYyChineseMonth:    "鸭鸭语文月课",
	CourseTypeYyChineseHalfYear: "鸭鸭语文半年课",
	CourseTypeYyChineseYear:     "鸭鸭语文年课",
}

func GetCourseTypeName(ctype int) string {
	return courseTypeMapping[ctype]
}

// 拉新属性位标志常量 (对应PHP的 Service_Data_AssistantNewCourse::PULL_NEW_DUTY_BIT_*)
const (
	PullNewDutyBitZero = 0   // 默认值
	PullNewDutyBit787  = 1   // 无
	PullNewDutyBit788  = 2   // 教师
	PullNewDutyBit789  = 4   // 销售
	PullNewDutyBit977  = 8   // OL
	PullNewDutyBit983  = 16  // 短训班
	PullNewDutyBit984  = 32  // 模拟考
	PullNewDutyBit1067 = 64  // 帮帮英语
	PullNewDutyBit1084 = 128 // 浣熊英语
	PullNewDutyBit1093 = 256 // 微信群服务
	PullNewDutyBit2286 = 512 // 加油课
)

// 课程业务线信息
const SERVICE_TYPE_DUXUE = 1
const SERVICE_TYPE_FD = 2

// 权益包固定课程性质,写字权益系统课(P)
const CONTRACT_COURSE_PRICE_TAG = 160

const (
	LESSON_PLAY_TYPE_LIVE        = 1 //直播
	LESSON_PLAY_TYPE_LUBO        = 6 //录播
	LESSON_PLAY_TYPE_AI_INTERACT = 3 //录播(按时解锁)
	LESSON_PLAY_TYPE_AI_HUDONG   = 5 //AI互动
)

// 定义课程状态常量
const (
	CourseStatusUnstart = 1 // 未开课
	CourseStatusIng     = 2 // 已开课
	CourseStatusEnd     = 3 // 已完课
)

// 定义课程状态映射
func GetCourseStatusMap() map[int]string {
	return map[int]string{
		CourseStatusUnstart: "未开课",
		CourseStatusIng:     "已开课",
		CourseStatusEnd:     "已完课",
	}
}

// 定义服务类型常量
const (
	ServiceTypeIn         = 1 // 服务中
	ServiceTypeBefore     = 2 // 待服务
	ServiceTypeAfter      = 3 // 服务结束
	ServiceTypeCourseList = 4 // 课程列表
)

// 定义服务类型映射

func GetServiceTypeMap() map[int]string {
	return map[int]string{
		ServiceTypeIn:         "服务中",
		ServiceTypeBefore:     "待服务",
		ServiceTypeAfter:      "服务结束",
		ServiceTypeCourseList: "课程列表",
	}
}

const (
	FuDaoCourse = 5
)
