package models

import (
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
)

var TblWechatBindTempEnterpriseDao tblWechatBindTempEnterpriseDao

type tblWechatBindTempEnterpriseDao struct {
}

// TblWechatBindTempEnterpriseData 个人微信手动标记绑定数据表
type TblWechatBindTempEnterpriseData struct {
	ID           int64 `gorm:"column:id" json:"id"`
	AssistantUid int64 `gorm:"column:assistant_uid" json:"assistantUid"`
	StudentUid   int64 `gorm:"column:student_uid" json:"studentUid"`
	IsBind       int   `gorm:"column:is_bind" json:"isBind"` // 是否绑定：0-未绑定 1-已绑定
	CreateTime   int64 `gorm:"column:create_time" json:"createTime"`
	UpdateTime   int64 `gorm:"column:update_time" json:"updateTime"`
}

// 个人微信绑定数据表名
func (TblWechatBindTempEnterpriseData) TableName() string {
	return "tblWechatBindTempEnterprise"
}

// GetEnterpriseBindData 获取企业微信手动绑定数据
func (t *tblWechatBindTempEnterpriseDao) GetByUid(ctx *gin.Context, assistantUid, studentUid int64) ([]TblWechatBindTempEnterpriseData, error) {
	var records []TblWechatBindTempEnterpriseData

	err := helpers.MysqlClient.WithContext(ctx).
		Where("assistant_uid = ? AND student_uid = ?", assistantUid, studentUid).
		Find(&records).Error

	if err != nil {
		return nil, err
	}

	return records, nil
}
