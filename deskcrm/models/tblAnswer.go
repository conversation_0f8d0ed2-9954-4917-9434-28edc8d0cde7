package models

import (
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
)

var TblAnswerDao tblAnswerDao

type tblAnswerDao struct {
}

type TblAnswer struct {
	ID           int64  `gorm:"column:id" json:"id"`
	StudentUid   int64  `gorm:"column:student_uid" json:"student_uid"`
	SurveyId     int64  `gorm:"column:survey_id" json:"survey_id"`
	SurveyType   int64  `gorm:"column:survey_type" json:"survey_type"`
	CourseId     int64  `gorm:"column:course_id" json:"course_id"`
	QuestionId   int64  `gorm:"column:question_id" json:"question_id"`
	TagId        int64  `gorm:"column:tag_id" json:"tag_id"`
	Answer       string `gorm:"column:answer" json:"answer"`
	AnswerMsg    string `gorm:"column:answer_msg" json:"answer_msg"`
	QuestionType int64  `gorm:"column:question_type" json:"question_type"`
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`
	UpdateTime   int64  `gorm:"column:update_time" json:"update_time"`
	Status       int64  `gorm:"column:status" json:"status"`
}

func (TblAnswer) TableName() string {
	return "tblAnswer"
}

// CheckAnswerExists 检查学生是否有对应类型的问卷答案
func (dao tblAnswerDao) CheckAnswerExists(ctx *gin.Context, surveyId int64, studentUid int64, surveyType int64) (bool, error) {
	var count int64
	err := helpers.LpcActiveMysqlClient.WithContext(ctx).Model(&TblAnswer{}).
		Where("survey_id = ? AND student_uid = ? AND survey_type = ? AND status = ?",
			surveyId, studentUid, surveyType, 1).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckNeedSurveyStatus 检查挖需问卷状态
func (dao tblAnswerDao) CheckNeedSurveyStatus(ctx *gin.Context, courseId int64, studentUid int64) (bool, error) {
	// 首先从tblBindInfo获取挖需问卷的surveyId
	var bindInfo TblBindInfo
	err := helpers.LpcActiveMysqlClient.WithContext(ctx).Model(&TblBindInfo{}).
		Where("course_id = ? AND survey_type = ? AND status = ?", courseId, SurveyTypeWaxu, 1).
		First(&bindInfo).Error

	if err != nil {
		// 如果没有找到绑定信息，返回false（未完成问卷）
		return false, nil
	}

	// 解析surveyId
	surveyId := bindInfo.SurveyID
	if surveyId == "" || surveyId == "0" {
		return false, nil
	}

	// 检查该学生是否有对应的问卷答案
	var count int64
	err = helpers.LpcActiveMysqlClient.WithContext(ctx).Model(&TblAnswer{}).
		Where("survey_id = ? AND student_uid = ? AND survey_type = ? AND status = ?",
			surveyId, studentUid, SurveyTypeWaxu, 1).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}
