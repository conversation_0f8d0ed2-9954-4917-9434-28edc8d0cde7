package models

import (
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
)

var TblShowWxCourseDao tblShowWxCourseDao

type tblShowWxCourseDao struct {
}

const (
	ShowWxCourseStatusInvalid = 0 // 无效
	ShowWxCourseStatusValid   = 1 // 有效
)

type TblShowWxCourse struct {
	ID         int64 `gorm:"column:id" json:"id"`
	CourseID   int64 `gorm:"column:course_id" json:"courseId"`
	Status     int   `gorm:"column:status" json:"status"`
	CreateTime int64 `gorm:"column:create_time" json:"createTime"`
	UpdateTime int64 `gorm:"column:update_time" json:"updateTime"`
}

func (TblShowWxCourse) TableName() string {
	return "tblShowWxCourse"
}

// GetListByCourseId 根据课程ID获取配置列表
func (t *tblShowWxCourseDao) GetListByCourseId(ctx *gin.Context, courseId int64, status int) ([]*TblShowWxCourse, error) {
	list := make([]*TblShowWxCourse, 0)
	query := helpers.MysqlClient.WithContext(ctx).Where("course_id = ?", courseId)

	if status > 0 {
		query = query.Where("status = ?", status)
	}

	err := query.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
