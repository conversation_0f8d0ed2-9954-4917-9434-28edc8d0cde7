package mesh

import (
	"crypto/md5"
	"deskcrm/api"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"deskcrm/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	getDeviceInfoListApi             = "/mesh/api/device/getdeviceinfolist"
	getStaffGroupPositionsApi        = "/mesh/api/staff/getstaffgrouppositions"
	getGroupDetailByIdsApi           = "/mesh/api/group/getgroupdetailbyids"
	getSelectedDeviceByStaffUidApi   = "/mesh/api/staff/getselecteddevicebystaffuid"
	getPersonInfosByAssistantUidsApi = "/mesh/api/achilles/getbindstaffsbydeviceuids"
	getWxTypeByDeviceUidsApi         = "/mesh/api/device/getwxtypebydeviceuids"

	// 资产类型, 与人管保持一致(因为sdk所以此处需要定义)
	PhoneTypePerson   = 1
	PhoneTypeBusiness = 2
)

const ApiMaxNum = 50
const NeedParentTreeForDetail = 1 //获取组织的上级树

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Mesh,
	}
	return c
}

// 获取资产用户信息
func (c *Client) GetUserInfoByDeviceUid(ctx *gin.Context, deviceUid int64) (retInfo AssistantInfo, err error) {
	// param
	if deviceUid == 0 {
		return retInfo, errors.New("deviceUid is invalid")
	}
	strDeviceUid := strconv.FormatInt(deviceUid, 10)

	// query: 设备信息
	deviceList, err := c.GetDeviceInfoList(ctx, []any{deviceUid})
	if err != nil {
		return retInfo, err
	}
	if len(deviceList.List) == 0 {
		return retInfo, errors.New("not found device info")
	}
	info, exist := deviceList.List[strDeviceUid]
	if !exist {
		return retInfo, errors.New("not found device info")
	}
	// 返回数据
	util.CopyObjFields(info, &retInfo.Record)
	retInfo.Record.Name = info.Nickname
	retInfo.Record.UserName = info.Nickname
	retInfo.Record.ShortUserName = info.ShortName
	retInfo.Record.PhoneType = PhoneTypeBusiness
	retInfo.Record.City = []int{info.City}
	retInfo.Record.Grade = []int{info.Grade}
	retInfo.Record.Subject = []int{info.Subject}
	return retInfo, nil
}

// 获取资产信息list
func (c *Client) GetDeviceInfoList(ctx *gin.Context, deviceUidList []any) (rsp DeviceList, err error) {
	req := map[string]interface{}{
		"deviceUids":      deviceUidList,
		"appId":           conf.Custom.Mesh.AppId,
		"ts":              time.Now().Unix(),
		"devicePhones":    []string{},
		"wxQrCodeChannel": "",
	}

	// 请求
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)
	res, err := c.cli.HttpGet(ctx, getDeviceInfoListApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	// 返回
	if _, err = api.DecodeResponse(ctx, res, &rsp); err != nil {
		return
	}
	return rsp, nil
}

func (c *Client) GetStaffGroupPositions(ctx *gin.Context, staffUid int64) (groupList []Group, err error) {
	var resp StaffGroupPositionsList
	req := map[string]interface{}{
		"staffUids": []int64{staffUid},
		"appId":     conf.Custom.Mesh.AppId,
		"ts":        time.Now().Unix(),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getStaffGroupPositionsApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp.List[strconv.FormatInt(staffUid, 10)].GroupList, nil
}

func (c *Client) GetGroupDetailById(ctx *gin.Context, groupId int64) (GroupDetail, error) {
	groupMap, err := c.GetGroupDetailByIds(ctx, []int64{groupId})
	if err != nil {
		return GroupDetail{}, err
	}

	detail, ok := groupMap[strconv.FormatInt(groupId, 10)]
	if !ok {
		return GroupDetail{}, errors.New("组织id不存在")
	}
	return detail, nil

}

/**
 * https://yapi.zuoyebang.cc/project/3908/interface/api/250401
 *根据组织ids批量获取组织详情、上级树、儿子组织列表、该组织所有人的uids(不包括离职的人)
 */
func (c *Client) GetGroupDetailByIds(ctx *gin.Context, groupIds []int64) (map[string]GroupDetail, error) {

	chunks := fwyyutils.ChunkArrayInt64(groupIds, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[string]GroupDetail)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(gids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetGroupDetailByIds panic err : %+v", r)
					components.Util.PanicTrace(ctx)
				}
			}()
			defer wg.Done()
			ret, err := c.GetGroupDetailSignal(ctx, gids, NeedParentTreeForDetail)
			if err != nil {
				zlog.Warnf(ctx, "GetGroupDetailByIds failed, groupIds:%+v, err:%s", gids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[string]GroupDetail)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseTeachersBatch panic err:%s", r)
				components.Util.PanicTrace(ctx)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

func (c *Client) GetGroupDetailSignal(ctx *gin.Context, groupIds []int64, needParentTree int) (groupDataMap map[string]GroupDetail, err error) {
	var resp GroupDetailList
	req := map[string]interface{}{
		"groupIds":       groupIds,
		"appId":          conf.Custom.Mesh.AppId,
		"needParentTree": needParentTree,
		"ts":             time.Now().Unix(),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getGroupDetailByIdsApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp.List, nil
}

func (c *Client) GetSelectedDeviceByStaffUid(ctx *gin.Context, staffUid int) (resp LoginDeviceInfo, err error) {
	params := map[string]interface{}{
		"staffUid": staffUid,
		"appId":    conf.API.Mesh.AppKey,
		"ts":       time.Now().Unix(),
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getSelectedDeviceByStaffUidApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp, nil
}

// GetPersonInfosByAssistantUids 根据业务UID获取真人账号信息
// 对应PHP的 Api_UserProfile::getPersonInfosByAssistantUids 方法
func (c *Client) GetPersonInfosByAssistantUids(ctx *gin.Context, assistantUids []int64) (GetPersonInfosByAssistantUidsResp, error) {
	// 1. 参数验证
	if len(assistantUids) == 0 {
		return nil, errors.New("assistantUids is empty")
	}

	// 2. 构建请求参数
	assistantUidsJSON, err := json.Marshal(assistantUids)
	if err != nil {
		return nil, fmt.Errorf("marshal assistantUids failed: %v", err)
	}

	req := map[string]interface{}{
		"key": string(assistantUidsJSON),
	}

	// 3. 加密参数
	if err := c.encryptParams(req); err != nil {
		return nil, fmt.Errorf("encrypt params failed: %v", err)
	}

	// 4. 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	// 5. 发送请求
	res, err := c.cli.HttpPost(ctx, getPersonInfosByAssistantUidsApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetPersonInfosByAssistantUids request err: %v", err)
		return nil, err
	}

	// 6. 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 7. 解析响应
	var resp GetPersonInfosByAssistantUidsResp
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

// encryptParams 加密请求参数，对应PHP的 Assistant_Unit_Api::encrypt 方法
func (c *Client) encryptParams(data map[string]interface{}) error {
	// 添加时间戳
	data["ts"] = time.Now().Unix()

	// 使用PHP中定义的APP_ID_MESH: "CC71C24B8F6BEDDA"
	appIdMesh := "CC71C24B8F6BEDDA"

	// 生成appKey
	appKey, err := c.genAppKey(appIdMesh, data)
	if err != nil {
		return err
	}

	// 添加加密参数
	data["appKey"] = appKey
	data["appId"] = appIdMesh

	return nil
}

// genAppKey 生成appKey，对应PHP的md5($clientId.json_encode($singData))逻辑
func (c *Client) genAppKey(appID string, data map[string]interface{}) (string, error) {
	// 转换所有值为字符串（对应PHP的array_map('strval', $data)）
	for k, v := range data {
		switch v := v.(type) {
		case string:
			// 字符串保持不变
		case int64:
			data[k] = strconv.FormatInt(v, 10)
		case int:
			data[k] = strconv.Itoa(v)
		default:
			// 对于其他类型，转换为JSON字符串
			jsonBytes, err := json.Marshal(v)
			if err != nil {
				return "", fmt.Errorf("marshal value failed: %v", err)
			}
			data[k] = string(jsonBytes)
		}
	}

	// 序列化为JSON（对应PHP的json_encode）
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// 为了和PHP的默认json_encode行为等价，需要替换一下
	jsonStr := strings.ReplaceAll(string(jsonBytes), `/`, `\/`)

	// 生成MD5（对应PHP的md5($clientId.json_encode($singData))）
	h := md5.New()
	if _, err := io.WriteString(h, appID); err != nil {
		return "", err
	}
	if _, err := io.WriteString(h, jsonStr); err != nil {
		return "", err
	}
	appKey := fmt.Sprintf("%x", h.Sum(nil))

	return appKey, nil
}

// GetWxTypeByDeviceUids 根据设备UID批量获取微信类型
// 对应PHP的 Assistant_Common_Service_User::getWxTypeByDeviceUids 方法
func (c *Client) GetWxTypeByDeviceUids(ctx *gin.Context, deviceUids []int64, kpAscription int) (GetWxTypeByDeviceUidsResp, error) {
	// 1. 参数验证
	if len(deviceUids) == 0 {
		zlog.Warnf(ctx, "GetWxTypeByDeviceUids params error: deviceUids is empty")
		return nil, errors.New("deviceUids is empty")
	}

	// 2. 设置默认资产归属：1-辅导，2-低幼，3-成人，4-督学，5-助教，6-IMC，7-无鲲鹏归属
	if kpAscription == 0 {
		kpAscription = 1
	}

	// 3. 构建请求参数
	req := map[string]interface{}{
		"deviceUids":   deviceUids,
		"kpAscription": kpAscription,
	}

	// 4. 加密参数
	if err := c.encryptParams(req); err != nil {
		return nil, fmt.Errorf("encrypt params failed: %v", err)
	}

	// 5. 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	// 6. 发送请求
	res, err := c.cli.HttpPost(ctx, getWxTypeByDeviceUidsApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetWxTypeByDeviceUids request err: %v", err)
		return nil, err
	}

	// 7. 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 8. 解析响应
	var resp GetWxTypeByDeviceUidsResp
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}
