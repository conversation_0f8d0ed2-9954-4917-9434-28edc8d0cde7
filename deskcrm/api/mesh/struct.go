package mesh

type StaffGroupPositionsList struct {
	List map[string]PositionGroup `json:"list"`
}
type PositionGroup struct {
	StaffUid  int     `json:"staffUid"`
	GroupList []Group `json:"groupList"`
}

type Group struct {
	ProductLine      int64  `json:"productLine"`
	GroupId          int64  `json:"groupId"`
	LearnType        int64  `json:"learnType"`
	GroupName        string `json:"groupName"`
	PositionId       int64  `json:"positionId"`
	PositionName     string `json:"positionName"`
	IsManager        int64  `json:"isManager"`
	StaffGroupLeader struct {
		LeaderUid      int64  `json:"leaderUid"`
		LeaderName     string `json:"leaderName"`
		RealLeaderUid  int64  `json:"realLeaderUid"`
		RealLeaderName string `json:"realLeaderName"`
	} `json:"staffGroupLeader"`
}

type GroupDetail struct {
	ID             int64            `json:"id"`
	Name           string           `json:"name"`
	ManagerUID     int64            `json:"managerUid"`
	LearnType      int64            `json:"learnType"`
	Level          int64            `json:"level"`
	ConfigID       int64            `json:"configId"`
	ParentID       int64            `json:"parentId"`
	LevelStr       string           `json:"levelStr"`
	NextLevelChild []NextLevelChild `json:"nextLevelChild"`
	ParentTree     ParentTree       `json:"parentTree"`
}

type NextLevelChild struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	ManagerUID int64  `json:"managerUid"`
	LearnType  int64  `json:"learnType"`
	Level      int64  `json:"level"`
	ConfigID   int64  `json:"configId"`
	ParentID   int64  `json:"parentId"`
	LevelStr   string `json:"levelStr"`
}

type ParentTree struct {
	ID         int64       `json:"id"`
	Name       string      `json:"name"`
	ManagerUID int64       `json:"managerUid"`
	LearnType  int64       `json:"learnType"`
	Children   *ParentTree `json:"children"`
}

type GroupDetailList struct {
	List map[string]GroupDetail `json:"list"`
}

type LoginDeviceInfo struct {
	DeviceId        int64  `json:"deviceId"`
	DeviceUid       int64  `json:"deviceUid"`
	Nickname        string `json:"nickname"`
	Phone           string `json:"phone"`
	WxNum           string `json:"wxNum"`
	KpAscription    int64  `json:"kpAscription"`
	AssignClassType int64  `json:"assignClassType"`
}

type DeviceList struct {
	List map[string]DeviceInfo `json:"list"`
}

// 资产信息
type DeviceInfo struct {
	DeviceID   int    `json:"deviceId"`
	Phone      string `json:"phone"`
	City       int    `json:"city"`
	CityName   string `json:"cityName"`
	Grade      int    `json:"grade"`
	GradeLevel int    `json:"gradeLevel"`
	Subject    int    `json:"subject"`
	/*	Type                      int      `json:"type"`
		WxRealID                  string   `json:"wxRealId"`
		WxType                    int      `json:"wxType"`
		WecomQRCode               string   `json:"wecomQRCode"`
		WecomUserID               string   `json:"wecomUserId"`
		WxNum                     string   `json:"wxNum"`
		WxQRCode                  string   `json:"wxQRCode"`
		DevicePic                 string   `json:"devicePic"`
		KpAscriptionList          []int    `json:"kpAscriptionList"`
		KpAscriptionShowList      []string `json:"kpAscriptionShowList"`
		AssignclassAscriptionList []int    `json:"assignclassAscriptionList"`
		StaffUID                  int64    `json:"staffUid"`
		StaffName                 string   `json:"staffName"`
		StaffStatus               int      `json:"staffStatus"`
		WecomCorpID               string   `json:"wecomCorpId"`
		ChannelQrCode             []struct {
			ConfigID string `json:"configId"`
			QrCode   string `json:"qrCode"`
			Channel  string `json:"channel"`
		} `json:"channelQrCode"`
		CustomerAcquisitionLink string `json:"customerAcquisitionLink"`
		WecomQRCodeURL          string `json:"wecomQRCodeUrl"`
		WxQRCodeURL             string `json:"wxQRCodeUrl"`
		DevicePicURL            string `json:"devicePicUrl"`*/
	Nickname  string `json:"nickname"`
	ShortName string `json:"shortName"`
}

// 资产信息变种版本, 此版本用于GetUserInfoByDeviceUid接口, 历史原因有此实现
type AssistantInfo struct {
	Record DeviceInfo4VariantVersion `json:"record"`
}

// 资产信息变种版本, 此版本用于GetUserInfoByDeviceUid接口
type DeviceInfo4VariantVersion struct {
	DeviceID   int    `json:"deviceId"`
	Phone      string `json:"phone"`
	City       []int  `json:"city"`
	CityName   string `json:"cityName"`
	Grade      []int  `json:"grade"`
	GradeLevel int    `json:"gradeLevel"`
	Subject    []int  `json:"subject"`
	/*	Type                      int      `json:"type"`
		WxRealID                  string   `json:"wxRealId"`
		WxType                    int      `json:"wxType"`
		WecomQRCode               string   `json:"wecomQRCode"`
		WecomUserID               string   `json:"wecomUserId"`
		WxNum                     string   `json:"wxNum"`
		WxQRCode                  string   `json:"wxQRCode"`
		DevicePic                 string   `json:"devicePic"`
		KpAscriptionList          []int    `json:"kpAscriptionList"`
		KpAscriptionShowList      []string `json:"kpAscriptionShowList"`
		AssignclassAscriptionList []int    `json:"assignclassAscriptionList"`
		StaffUID                  int64    `json:"staffUid"`
		StaffName                 string   `json:"staffName"`
		StaffStatus               int      `json:"staffStatus"`
		WecomCorpID               string   `json:"wecomCorpId"`
		ChannelQrCode             []struct {
			ConfigID string `json:"configId"`
			QrCode   string `json:"qrCode"`
			Channel  string `json:"channel"`
		} `json:"channelQrCode"`
		CustomerAcquisitionLink string `json:"customerAcquisitionLink"`
		WecomQRCodeURL          string `json:"wecomQRCodeUrl"`
		WxQRCodeURL             string `json:"wxQRCodeUrl"`
		DevicePicURL            string `json:"devicePicUrl"`*/

	// GetUserInfoByDeviceUid接口用到的额外字段, 以下字段非mesh服务返回
	Name          string `json:"name"`
	UserName      string `json:"userName"`
	ShortUserName string `json:"shortUserName"`
	PhoneType     int    `json:"phoneType"`
}

// GetPersonInfosByAssistantUidsResp 根据业务UID获取真人账号信息响应
type GetPersonInfosByAssistantUidsResp map[string]PersonInfo

// PersonInfo 真人账号信息
type PersonInfo struct {
	StaffUid  int64  `json:"staffUid"`  // 真人UID
	StaffName string `json:"staffName"` // 真人姓名
	// 其他字段根据实际API返回结构补充
}

// GetWxTypeByDeviceUidsResp 根据设备UID批量获取微信类型响应
type GetWxTypeByDeviceUidsResp map[string]int

// WxTypeInfo 微信类型信息
type WxTypeInfo struct {
	DeviceUid int64 `json:"deviceUid"` // 设备UID
	WxType    int   `json:"wxType"`    // 微信类型：1-个微，2-企微
}
