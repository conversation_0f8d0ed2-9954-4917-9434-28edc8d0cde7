package mesh

import (
	"deskcrm/helpers"
	struArk "deskcrm/stru/ark"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()
	//esclient
	//helpers.InitCos()
	helpers.InitMysql()
	helpers.InitRedis()
}

func TestGetConfigForString(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ruleConfigStr := `{"apps":[1,2],"feConfig":{"label":"\u662f\u5426\u76f4\u64ad\u5b8c\u8bfe","prop":"is_inclass_teacher_room_attend_finish","parameter":"is_inclass_teacher_room_attend_finish","minWidth":"100","slot":"true","cname":"common","multiple":"true"},"key":"is_inclass_teacher_room_attend_finish","oriName":"\u662f\u5426\u76f4\u64ad\u5b8c\u8bfe","customName":"\u662f\u5426\u76f4\u64ad\u5b8c\u8bfe","type":1,"info":"\u662f\u5426\u76f4\u64ad\u5b8c\u8bfe","isExportExcle":true,"function":"","isSelect":false,"isDisabled":false,"fieldType":5,"sort":101,"newFieldType":1006,"secondGroup":"","filterMapUseJsonImport":false,"feConfigUseJsonImport":false,"exportFieldMapUseJsonImport":false,"dataSourceInfo":{"field":"is_inclass_teacher_room_attend_finish","returnField":"is_inclass_teacher_room_attend_finish","valueType":"int"},"dataSourceInfoUseJsonImport":false,"source":"s_2011","togetherType":2,"serviceConfig":{"":""}}`
	ruleConfig := &struArk.RuleFieldStruct{}
	_ = jsoniter.UnmarshalFromString(ruleConfigStr, ruleConfig)
	resp := []interface{}{}
	fmt.Printf("ctx:%+v, err:%+v", ctx, resp)
}

func Test_GetPersonInfosByAssistantUids(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	assistantUids := []int64{2000021005}
	resp, err := NewClient().GetPersonInfosByAssistantUids(ctx, assistantUids)
	if err != nil {
		t.Log(err)
		return
	}
	t.Log(resp)
}
