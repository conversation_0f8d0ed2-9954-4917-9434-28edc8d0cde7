package achilles

import (
	"deskcrm/helpers"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()
}

func TestClient_GetInfoByLessonId(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	result, err := NewClient().GetInfoByLessonId(ctx, []string{"543201"}, []string{})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证返回的数据结构
	if lessonInfo, exists := result["543201"]; exists {
		fmt.Printf("LessonId: %d\n", lessonInfo.LessonId)
		fmt.Printf("LessonName: %s\n", lessonInfo.LessonName)
		fmt.Printf("OutlineId: %d\n", lessonInfo.OutlineId)
		fmt.Printf("StartTime: %d\n", lessonInfo.StartTime)
		fmt.Printf("StopTime: %d\n", lessonInfo.StopTime)
		fmt.Printf("Status: %d\n", lessonInfo.Status)
		fmt.Printf("CourseID: %v\n", lessonInfo.CourseId)
		fmt.Printf("LessonType: %d\n", lessonInfo.LessonType)
		fmt.Printf("PlayType: %d\n", lessonInfo.PlayType)
		fmt.Printf("Mode: %d\n", lessonInfo.Mode)
		fmt.Printf("HasHomework: %d\n", lessonInfo.HasHomework)
		fmt.Printf("HasPlayback: %d\n", lessonInfo.HasPlayback)
		fmt.Printf("IsClassing: %d\n", lessonInfo.IsClassing)
		fmt.Printf("TeacherUid: %d\n", lessonInfo.TeacherUid)
		fmt.Printf("LessonStatus: %d\n", lessonInfo.LessonStatus)

		// 验证关键字段
		assert.Equal(t, 543201, lessonInfo.LessonId)
		assert.Equal(t, "TEST2", lessonInfo.LessonName)
		assert.Equal(t, 1149674, lessonInfo.OutlineId)
		assert.Equal(t, []int{552286}, lessonInfo.CourseId)
	} else {
		t.Error("Expected lesson info for ID 543201 not found")
	}
}

func TestClient_GetInfoByLessonId_WithFieldFilter(t *testing.T) {
	// 创建一个mock的ProcessedLessonInfo来测试字段过滤功能
	client := NewClient()

	// 创建测试数据
	testData := &ProcessedLessonInfo{
		LessonInfo: LessonInfo{
			LessonId:   543201,
			LessonName: "TEST2",
			OutlineId:  1149674,
			StartTime:  1753351981,
			StopTime:   1753358399,
			CourseId:   []int{552286},
			LessonType: 1,
			PlayType:   1,
			Mode:       1,
		},
		Teacher:      TeacherLesson{TeacherUid: 3000018868},
		LessonStatus: 0,
		TeacherUid:   3000018868,
		IsClassing:   1,
	}

	// 测试字段过滤功能
	filtered := client.filterField(testData, []string{"lessonId", "outlineId"})

	// 验证过滤后的字段
	fmt.Printf("Filtered LessonId: %d\n", filtered.LessonId)
	fmt.Printf("Filtered OutlineId: %d\n", filtered.OutlineId)
	fmt.Printf("Filtered LessonName: %s\n", filtered.LessonName) // 应该为空，因为没有在fieldList中

	// 验证指定的字段被保留
	assert.Equal(t, 543201, filtered.LessonId)
	assert.Equal(t, 1149674, filtered.OutlineId)

	// 验证未指定的字段被过滤为零值
	assert.Equal(t, "", filtered.LessonName)
	assert.Equal(t, []int(nil), filtered.CourseId)
	assert.Equal(t, int64(0), filtered.StartTime)
	assert.Equal(t, int64(0), filtered.StopTime)
	assert.Equal(t, 0, filtered.LessonType)
	assert.Equal(t, 0, filtered.PlayType)
	assert.Equal(t, 0, filtered.Mode)

	// 验证处理后的字段也被过滤
	assert.Equal(t, 0, filtered.TeacherUid)
	assert.Equal(t, 0, filtered.LessonStatus)
	assert.Equal(t, 0, filtered.IsClassing)
}

func TestProcessedLessonInfo_JSONSerialization(t *testing.T) {
	// 测试ProcessedLessonInfo的JSON序列化
	testData := &ProcessedLessonInfo{
		LessonInfo: LessonInfo{
			LessonId:   543201,
			LessonName: "TEST2",
			OutlineId:  1149674,
			StartTime:  1753351981,
			CourseId:   []int{552286},
		},
		Teacher:      TeacherLesson{TeacherUid: 3000018868},
		LessonStatus: 0,
		TeacherUid:   3000018868,
		IsClassing:   1,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(testData)
	assert.NoError(t, err)

	// 验证JSON结构（LessonInfo字段应该被平铺）
	var jsonMap map[string]interface{}
	err = json.Unmarshal(jsonData, &jsonMap)
	assert.NoError(t, err)

	// 验证LessonInfo字段被平铺到顶层
	assert.Equal(t, float64(543201), jsonMap["lessonId"])
	assert.Equal(t, "TEST2", jsonMap["lessonName"])
	assert.Equal(t, float64(1149674), jsonMap["outlineId"])
	assert.Equal(t, float64(1753351981), jsonMap["startTime"])

	// 验证处理后的字段
	assert.Equal(t, float64(3000018868), jsonMap["teacherUid"])
	assert.Equal(t, float64(0), jsonMap["lessonStatus"])
	assert.Equal(t, float64(1), jsonMap["isClassing"])

	fmt.Printf("JSON output: %s\n", string(jsonData))
}
