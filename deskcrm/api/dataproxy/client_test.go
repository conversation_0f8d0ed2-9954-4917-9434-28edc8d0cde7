package dataproxy

import (
	"deskcrm/helpers"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()
}

func TestClient_GetLpcListByCourseStudent(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	param := GetLpcListByCourseStudentParam{
		CourseId:   552459,
		StudentUid: 2135252654,
		Fields:     []string{"lpc_uid", "course_id", "student_uid", "lesson_id", "leads_id", "playback_time"},
	}
	result, err := NewClient().GetLpcListByCourseStudent(ctx, param)
	assert.NoError(t, err)
	fmt.Printf("result: %v\n", result)
}
