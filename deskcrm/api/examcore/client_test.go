package examcore

import (
	"deskcrm/helpers"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()
}

func TestClient_GetRelation(t *testing.T) {
	// 测试数据
	bindList := []string{"cpu_123:20"}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 调用方法
	relationResp, err := NewClient().GetRelation(ctx, bindList)
	assert.NoError(t, err)
	fmt.Printf("GetRelation response: %v\n", relationResp)
}

func TestClient_GetAnswer(t *testing.T) {
	// 测试数据
	answerKeyList := []string{"answer_123", "answer_456"}

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 调用方法
	answerResp, err := NewClient().GetAnswer(ctx, answerKeyList)
	assert.NoError(t, err)
	fmt.Printf("GetAnswer response: %v\n", answerResp)
}

func TestClient_GetBottomTestSubjectList(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	result, err := NewClient().GetBottomTestSubjectList(ctx, 132658)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	fmt.Printf("result: %v\n", result)
}