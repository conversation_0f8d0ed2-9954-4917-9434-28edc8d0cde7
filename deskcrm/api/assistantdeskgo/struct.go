package assistantdeskgo

type GetPreClassDetailReq struct {
	CourseId    int64   `json:"courseId"`
	LessonId    int64   `json:"lessonId"`
	StudentUids []int64 `json:"studentUids"`
}

type GetPreClassDetailRsp struct {
	PreClassDetails []*PreClassDetail `json:"preClassDetails"`
	FailedStudents  []int64           `json:"failedStudents"`
}

type PreClassDetail struct {
	Id               int64  `json:"id"`
	StudentUid       int64  `json:"studentUid"`
	CourseId         int64  `json:"courseId"`
	LessonId         int64  `json:"lessonId"`
	AssistantUid     int64  `json:"assistantUid"`
	PreAttend        int    `json:"preAttend"`
	CreateTime       int64  `json:"createTime"`
	UpdateTime       int64  `json:"updateTime"`
	PreAttendTime    int64  `json:"preAttendTime"`
	LeaveSeason      string `json:"leaveSeason"`
	FirstLeaveReason string `json:"firstLeaveReason"`
	ContentTime      string `json:"contentTime"`
	RemindTime       string `json:"remindTime"`
	AccompanyTag     string `json:"accompanyTag"`
	IsSyncRemind     int    `json:"isSyncRemind"`
	RemindId         int64  `json:"remindId"`
}

type GrayHitReq struct {
	PersonUid int64  `json:"personUid" form:"personUid"`
	Key       string `json:"key" form:"key"`
}

type OpLogResp struct {
	OpLogId          int64  `json:"opLogId"`
	ManualIntention  int64  `json:"manualIntention"`  //手动意向
	ManualCallStatus int64  `json:"manualCallStatus"` //手动外呼
	ManualRemark     string `json:"manualRemark"`     //手动备注
	CallLastTime     string `json:"callLastTime"`     //当前老师下线索最新接通时间
}

type CallCountInfo struct {
	SuccessCallCount int64 `json:"successCallCount"`
	TotalcallCount   int64 `json:"totalcallCount"`
}

type LearnReportInfo struct {
	LearnReportStatus   int64  `json:"learnReportStatus"`
	LearnReportUrl      string `json:"learnReportUrl"`
	LearnReportWriteUrl string `json:"learnReportWriteUrl"`
}

// GetAIRecordReq AI记录查询请求参数
type GetAIRecordReq struct {
	CourseId   int64 `json:"courseId"`   // 课程ID
	StudentUid int64 `json:"studentUid"` // 学生UID
	PageTab    int   `json:"pageTab"`    // 页面标签类型 (0=查看全部, 2=只看自己)
	Page       int   `json:"page"`       // 页码
	PageSize   int   `json:"pageSize"`   // 每页大小
}

// GetAIRecordResp AI记录查询响应
type GetAIRecordResp struct {
	CallDetailList []AICallDetail `json:"callDetailList"` // AI通话记录列表
	Total          int            `json:"total"`          // 总数
}

// CallContent 通话内容详情
type CallContent struct {
	SentenceId int     `json:"sentence_id"` // 句子ID
	StartTime  float64 `json:"start_time"`  // 开始时间（秒）
	EndTime    float64 `json:"end_time"`    // 结束时间（秒）
	Role       int     `json:"role"`        // 角色（1=用户，2=客服等）
	Content    string  `json:"content"`     // 内容文本
}

// CallTag 通话标签详情
type CallTag struct {
	Label       string `json:"label"`       // 标签名称
	Value       string `json:"value"`       // 标签值
	Credibility string `json:"credibility"` // 可信度
	Remark      string `json:"remark"`      // 备注
}

// AICallDetail AI通话记录详情
type AICallDetail struct {
	CallId      string        `json:"callId"`      // 通话ID
	StartTime   int64         `json:"startTime"`   // 开始时间戳（毫秒）
	StopTime    int64         `json:"stopTime"`    // 结束时间戳（毫秒）
	Status      int           `json:"status"`      // 状态
	Type        int           `json:"type"`        // 类型
	RecordFile  string        `json:"recordFile"`  // 录音文件URL
	SourceType  string        `json:"sourceType"`  // 来源类型
	CourseName  string        `json:"courseName"`  // 课程名称
	CourseId    int64         `json:"courseId"`    // 课程ID
	CallMode    int           `json:"callMode"`    // 通话模式
	CallModeStr string        `json:"callModeStr"` // 通话模式字符串
	Duration    int64         `json:"duration"`    // 通话时长（毫秒）
	Content     []CallContent `json:"content"`     // 通话内容数组
	Abstract    string        `json:"abstract"`    // 摘要
	Tags        []CallTag     `json:"tags"`        // 标签数组
	Accurate    int           `json:"accurate"`    // 准确度
	Inaccurate  int           `json:"inaccurate"`  // 不准确度
	CreateTime  int64         `json:"createTime"`  // 创建时间戳
	CurrentTime int64         `json:"currentTime"` // 当前时间戳
	Version     int           `json:"version"`     // 版本
}
