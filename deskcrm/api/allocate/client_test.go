package allocate

import (
	"deskcrm/helpers"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()
}

func TestClient_GetLeadsByBatchCourseIdUid(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	//courseIds := []int64{548515, 548273, 547866, 545818, 545662, 544660, 547621, 544944, 550387, 548128, 546198, 545869, 545589, 549344, 548869, 548860, 545571, 545327, 545324, 550087, 549157, 546780, 546467, 545828, 545501, 544949, 550375, 549009, 543829, 545582, 545578, 544749, 550084, 548442, 546867, 545190, 545563, 544877, 552066, 548270, 545486}
	courseIds := []int64{552459}
	studentUid := int64(2135252654)
	result, err := NewClient().GetLeadsByBatchCourseIdUid(ctx, courseIds, studentUid)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	fmt.Printf("result: %v\n", result)
}
