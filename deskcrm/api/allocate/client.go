package allocate

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"errors"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

const (
	getNormalLeadsByCourseAssistantApi = "/allocate/api/getnormalleadsbycourseassistant"
	GetLeadsByIdsApi                   = "/allocate/api/getleadsbyids" //http://yapi.zuoyebang.cc/project/5857/interface/api/191194
	GetValidNoCourseLeadsListApi       = "/allocate/api/getvalidnocourseleadslist"
	GetNoCourseLeadsInfoApi            = "/allocate/api/getnocourseleadslistbyLeadsids" //http://yapi.zuoyebang.cc/project/5857/interface/api/191194
	getLeadsByBatchCourseIdUidApi      = "/allocate/api/getleadsbybatchcourseiduid" //http://yapi.zuoyebang.cc/project/5857/interface/api/237980
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Allocate,
	}
	return c
}

// 根据课程，老师获取服务状态正常的leads(有效例子和有效期过期leads)
// http://yapi.zuoyebang.cc/project/5857/interface/api/280293
func (c *Client) GetNormalLeadsByCourseAssistant(ctx *gin.Context, courseId, assistantUid int64) (leadMap map[int64]*NormalLeadsByCourseAssistant, err error) {
	if courseId == 0 || assistantUid == 0 {
		return nil, errors.New("param error")
	}
	leadMap = map[int64]*NormalLeadsByCourseAssistant{}
	pageSize := 1000
	// 暂定 5000 上限
	for i := 1; i <= 5; i++ {
		req := map[string]interface{}{
			"courseId":     courseId,
			"assistantUid": assistantUid,
			"page":         i,
			"pageSize":     pageSize,
		}

		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, apiErr := c.cli.HttpPost(ctx, getNormalLeadsByCourseAssistantApi, opts)
		if apiErr = api.ApiHttpCode(ctx, res); apiErr != nil {
			return
		}

		resp := NormalLeadsByCourseAssistantResp{}
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return
		}
		for idx, _ := range resp.List {
			leadMap[resp.List[idx].LeadsId] = resp.List[idx]
		}
		if len(resp.List) < pageSize {
			break
		}
	}

	return leadMap, nil
}

// 根据用户获取有效的leads信息
// http://yapi.zuoyebang.cc/project/5857/interface/api/191194
func (c *Client) GetLeadsByIds(ctx *gin.Context, leadsIds []int64) (leadMap map[int64]*LeadsInfo, err error) {
	if len(leadsIds) == 0 {
		return nil, errors.New("param error")
	}
	resp := map[int64]*LeadsInfo{}
	leadsIdsArr := fwyyutils.ChunkArrayInt64(leadsIds, 50)
	//最多取2次，2000条
	for _, chunkLeadsIds := range leadsIdsArr {
		req := map[string]interface{}{
			"leadsIds": chunkLeadsIds,
		}

		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, apiErr := c.cli.HttpPost(ctx, GetLeadsByIdsApi, opts)
		if apiErr = api.ApiHttpCode(ctx, res); apiErr != nil {
			return
		}

		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return
		}
		for _, detail := range resp {
			leadMap[detail.LeadsId] = detail
		}
	}

	return leadMap, nil
}

func (c *Client) GetValidNoCourseLeadsListApi(ctx *gin.Context, assistantUid int64) (leadMap map[int64]*NoClassLeads, err error) {
	if assistantUid == 0 {
		return nil, errors.New("param error")
	}
	leadMap = map[int64]*NoClassLeads{}
	resp := NoClassLeadsResp{}
	req := map[string]interface{}{
		"deviceUid": assistantUid,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, apiErr := c.cli.HttpPost(ctx, GetValidNoCourseLeadsListApi, opts)
	if apiErr = api.ApiHttpCode(ctx, res); apiErr != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	for idx, _ := range resp.List {
		leadMap[resp.List[idx].LeadsId] = resp.List[idx]
	}

	return leadMap, nil
}

func (c *Client) GetNoCourseLeadsInfo(ctx *gin.Context, leadsIds []int64) (leadMap map[int64]*NoCourseLeadsInfo, err error) {
	if len(leadsIds) == 0 {
		return nil, errors.New("param error")
	}
	leadMap = map[int64]*NoCourseLeadsInfo{}
	resp := NoCourseLeadsResp{}
	leadsIdsArr := fwyyutils.ChunkArrayInt64(leadsIds, 50)
	for _, chunkLeadsIds := range leadsIdsArr {
		req := map[string]interface{}{
			"leadsIds": fwyyutils.JoinArrayInt64ToString(chunkLeadsIds, ","),
		}

		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, apiErr := c.cli.HttpGet(ctx, GetNoCourseLeadsInfoApi, opts)
		if apiErr = api.ApiHttpCode(ctx, res); apiErr != nil {
			return
		}

		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return
		}
		for _, detail := range resp.List {
			d := detail
			leadMap[detail.LeadsId] = &d
		}
	}

	return leadMap, nil
}

// GetLeadsByBatchCourseIdUid 根据课程ID和学生UID批量获取leads信息
func (c *Client) GetLeadsByBatchCourseIdUid(ctx *gin.Context, courseIds []int64, studentUid int64) ([]BatchLeadsInfo, error) {
	if len(courseIds) == 0 || studentUid == 0 {
		return nil, errors.New("param error")
	}

	// 构建请求参数
	var params []BatchCourseIdUidParam
	for _, courseId := range courseIds {
		params = append(params, BatchCourseIdUidParam{
			StuUid:   studentUid,
			CourseId: courseId,
		})
	}

	var allLeads []BatchLeadsInfo
	
	// 分批处理，每批50个参数
	chunkedParams := chunkBatchParams(params, 50)
	for _, chunk := range chunkedParams {
		req := map[string]interface{}{
			"params":   chunk,
			"onlyLast": 1,
		}

		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, getLeadsByBatchCourseIdUidApi, opts)
		if err != nil {
			return nil, err
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		var resp BatchLeadsData
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return nil, err
		}

		allLeads = append(allLeads, resp.LeadsList...)
	}

	return allLeads, nil
}

// chunkBatchParams 将参数数组分块
func chunkBatchParams(params []BatchCourseIdUidParam, size int) [][]BatchCourseIdUidParam {
	var chunks [][]BatchCourseIdUidParam
	for i := 0; i < len(params); i += size {
		end := min(i + size, len(params))
		chunks = append(chunks, params[i:end])
	}
	return chunks
}
