package assistantdesk

import (
	"deskcrm/controllers/http/ui/input/inputArkUI"
	"deskcrm/controllers/http/ui/output/outputArkUI"
	"encoding/json"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
)

type GetCourseServicesParams struct {
	AssistantUID int64 `json:"assistantUid"`
	CourseID     int64 `json:"courseId"`
}

type GetCourseArkInfoParams struct {
	CourseID int64 `json:"courseId"`
}

// GetCourseServices
type GetCourseServicesResp struct {
	ServiceList []*CourseServiceList `json:"serviceList"`
}

type CourseServiceList struct {
	ID                int64                     `json:"id"` //服务模式id
	ServiceName       string                    `json:"serviceName"`
	TplId             int64                     `json:"tplId"`
	ServiceId         int64                     `json:"serviceId"`
	ParentServiceId   int64                     `json:"parentServiceId"`
	ParentServiceName string                    `json:"parentServiceName"`
	Features          CourseServiceListFeatures `json:"features"`
}

type CourseServiceListFeatures struct {
	Tools           []int64      `json:"tools,omitempty"`
	BroadcastButton []int64      `json:"broadcastButton,omitempty"`
	TaskList        TaskList     `json:"taskList,omitempty"`
	LessonList      []LessonInfo `json:"lessonList,omitempty"`
}

type LessonInfo struct {
	CourseId   int64  `json:"courseId"`
	LessonId   int64  `json:"lessonId"`
	LessonName string `json:"lessonName"`
}

type TaskList struct {
	Label string         `json:"label,omitempty"`
	List  []TaskListItem `json:"list,omitempty"`
}

type TaskListItem struct {
	TaskId         int64  `json:"taskId,omitempty"`
	TaskName       string `json:"taskName,omitempty"`
	TaskStatusName string `json:"taskStatusName,omitempty"`
}

// GetCourseArkInfo
type GetCourseArkInfoResp struct {
	ServiceList []*GetCourseArkInfoServiceList `json:"serviceList"`
}

type GetCourseArkInfoServiceList struct {
	ServiceId    int64           `json:"serviceId"`
	FieldMapTree []*FieldMapTree `json:"fieldMapTree"`
}

type FieldMapTree struct {
	FieldTypeName string         `json:"fieldTypeName"`
	SecondGroup   []*SecondGroup `json:"secondGroup"`
	Hide          int64          `json:"hide"`
}

type SecondGroup struct {
	Name string             `json:"name"`
	List []*SecondGroupList `json:"List"`
}

type SecondGroupList struct {
	Key          string          `json:"key"`
	Name         string          `json:"name"`
	RuleName     string          `json:"ruleName"`
	RuleId       int64           `json:"ruleId"`
	Hover        string          `json:"hover"`
	Hide         int64           `json:"hide"`
	FilterMap    json.RawMessage `json:"filterMap"`
	Sort         int64           `json:"sort"`
	FeConfig     json.RawMessage `json:"feConfig"`
	NewFieldType int64           `json:"newFieldType"`
	SecondGroup  string          `json:"secondGroup"`
}

type GetExportStudentDataParams struct {
	AssistantUID int64         `json:"assistantUid"`
	PersonUID    int64         `json:"personUid"`
	ServiceId    int64         `json:"serviceId"`
	CourseId     int64         `json:"courseId"`
	LessonId     int64         `json:"lessonId"`
	TaskId       int64         `json:"taskId"`
	Sorts        []StudentSort `json:"sorts"`
}

type StudentSort struct {
	Key  string `json:"key"`
	Sort string `json:"sort"`
}

type GetExportStudentDataResp struct {
	Data [][]string `json:"data"`
}

// GetStudentListDataParams 学生列表相关接口通用入参
type GetStudentListDataParams struct {
	AssistantUID int64  `json:"assistantUid"`
	PersonUID    int64  `json:"personUid"`
	ServiceId    int64  `json:"serviceId"`
	CourseId     int64  `json:"courseId"`
	LessonId     int64  `json:"lessonId"`
	TaskId       int64  `json:"taskId"`
	TplId        int64  `json:"tplId"`
	Timestamp    int64  `json:"timestamp"`
	Keyword      string `json:"keyword"`
	Filter       string `json:"filter"`
	Pn           int64  `json:"pn"`
	Rn           int64  `json:"rn"`
	Sorts        string `json:"sorts"`
	UseNew       int64  `json:"useNew"`
}

type GetStudentListDataResp struct {
	AssistantUid   int64                    `json:"assistantUid"`
	CourseName     string                   `json:"courseName"`
	CourseType     int                      `json:"courseType"`
	OnlineTime     string                   `json:"onlineTime"`
	TeacherName    string                   `json:"teacherName"`
	AssistantPhone string                   `json:"assistantPhone"`
	GradeStage     int                      `json:"gradeStage"`
	GradeId        int                      `json:"gradeId"`
	IsLessonFinish int                      `json:"isLessonFinish"`
	SubjectId      int                      `json:"subjectId"`
	NewCourseType  int                      `json:"newCourseType"`
	QueId          interface{}              `json:"queId"`
	ShortQueUrl    string                   `json:"shortQueUrl"`
	List           []map[string]interface{} `json:"list"`
	FieldMapTree   []FieldMapTreeNew        `json:"fieldMapTree"`
	Total          int                      `json:"total"`
	SelectedCnt    int                      `json:"selectedCnt"`
	AllStudentsIDs []int64                  `json:"allStudentsIDs"`
}

type GetWxStudentListDataResp struct {
	StudentList        []CommonStudentList `json:"studentList"`
	NotBindStudentList []CommonStudentList `json:"notBindStudentList"`
	ErrStudentUids     []int64             `json:"errStudentUids"`
}

type GetCommonStudentListDataResp struct {
	StudentList    []CommonStudentList `json:"studentList"`
	ErrStudentUids []int64             `json:"errStudentUids"`
}

type CommonStudentList struct {
	Reason      string      `json:"reason"`
	StudentUID  int64       `json:"studentUid"`
	StudentName string      `json:"studentName"`
	UNAME       string      `json:"uname"`
	Phone       string      `json:"phone"`
	RegPhone    string      `json:"regPhone"`
	ClassID     int64       `json:"classId"`
	ClassName   string      `json:"className"`
	UseLabel    int         `json:"useLabel"`
	WxIdsList   []WxIdsInfo `json:"wxIdsList"`
}

type WxIdsInfo struct {
	ID             int64       `json:"id"`
	StaffUID       int64       `json:"staffUid"`
	UserID         string      `json:"userId"`
	CorpID         string      `json:"corpId"`
	StudentUID     int64       `json:"studentUid"`
	RemoteID       string      `json:"remoteId"`
	RelationType   int         `json:"relationType"`
	Deleted        int         `json:"deleted"`
	CreateTime     int64       `json:"createTime"`
	UpdateTime     string      `json:"updateTime"`
	ExtFlag        int         `json:"extFlag"`
	ExtData        interface{} `json:"extData"`
	WeixinID       string      `json:"weixinId"`
	Remark         string      `json:"remark"`
	WeixinName     string      `json:"weixinName"`
	WeixinNickName string      `json:"weixinNickName"`
	WeixinAvatar   string      `json:"weixinAvatar"`
}

type FieldMapTreeNew struct {
	FieldTypeName string           `json:"fieldTypeName"`
	SecondGroup   []SecondGroupNew `json:"secondGroup"`
	Fixed         string           `json:"fixed"`
	Width         int              `json:"width"`
	Hide          int              `json:"hide"`
	StyleSort     int              `json:"styleSort"`
	Key           int              `json:"key"`
}

type SecondGroupNew struct {
	Name string           `json:"name"`
	List []FieldConfigNew `json:"list"`
	Key  string           `json:"key"`
	Hide int              `json:"hide"`
}

type FieldConfigNew struct {
	Key          string                   `json:"key"`
	Name         string                   `json:"name"`
	RuleName     string                   `json:"ruleName"`
	RuleId       int                      `json:"ruleId"`
	Hover        string                   `json:"hover"`
	Hide         int                      `json:"hide"`
	FilterMap    []map[string]interface{} `json:"filterMap"`
	Sort         int                      `json:"sort"`
	FeConfig     json.RawMessage          `json:"feConfig"`
	NewFieldType int                      `json:"newFieldType"`
	SecondGroup  string                   `json:"secondGroup"`
	StyleSort    int                      `json:"styleSort"`
	StyleWidth   int                      `json:"styleWidth"`
}

type CustomTagListDetail struct {
	ID           int            `json:"id"`
	CourseId     int            `json:"courseId"`
	AssistantUid int64          `json:"assistantUid"`
	StudentUid   int64          `json:"studentUid"`
	TagType      int            `json:"tagType"`
	TagsArr      []CustomTagArr `json:"tagsArr"`
}

type CustomTagArr struct {
	Tag   string `json:"tag"`
	Color string `json:"color"`
}

type GetUidsByTagResp struct {
	StudentUids []int64 `json:"studentUids"`
}

type GetArkFormatStudentListParam struct {
	AssistantUID         int64  `json:"assistantUid"`
	PersonUID            int64  `json:"personUid"`
	CourseId             int64  `json:"courseId"`
	LessonId             int64  `json:"lessonId"`
	TaskId               int64  `json:"taskId"`
	ServiceId            int64  `json:"serviceId"`
	TplId                int64  `json:"tplId"`
	Timestamp            int64  `json:"timestamp"`
	StudentUids          string `json:"studentUids"`                            //json string
	LeadsIdMapStudentUid string `json:"leadsIdMapStudentUid"`                   //json string
	NeedFieldKeys        string `json:"needFieldKeys"`                          //json string
	DataRangeSelect      string `json:"dataRangeSelect" form:"dataRangeSelect"` // json string
	OriRule              string `json:"oriRule" form:"oriRule"`                 // json string
}

type GetArkFormatStudentListResp struct {
	StudentList []map[string]interface{} `json:"studentList"`
}

type GetKeyConstParams struct {
	Key string `json:"key"`
}

type GetExamBindStatusParams struct {
	LessonId int64 `json:"lessonId"`
	BindType int   `json:"bindType"`
}

type CollectionParam inputArkUI.CollectionParam

type CollectionResp struct {
	Collection []outputArkUI.CollectionItem `json:"collection"`
	OverView   []outputArkUI.OverViewItem   `json:"overview"`
	Sort       []arkgo.CollectionStyle      `json:"sort"`
}
