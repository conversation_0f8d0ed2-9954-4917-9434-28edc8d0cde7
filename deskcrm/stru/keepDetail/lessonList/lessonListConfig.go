package lessonList

type LessonListConfigStru []*LessonListGroupStru

type LessonListGroupStru struct {
	GroupKey  string
	GroupName string
	RuleList  []*LessonRuleConfigStru
}

type LessonRuleConfigStru struct {
	Key        string
	Lable      string
	Function   string
	Sort       int
	FusingRule FusingRuleStru
}
type FusingRuleStru struct {
	TimeoutWarning int64 `json:"timeoutWarning"` //熔断报警墙 单位：ms
	TimeoutFusing  int64 `json:"timeoutFusing"`  //熔断墙 单位：ms
	FusingDuration int64 `json:"fusingDuration"` //熔断时长 单位：s
	Duration       int64 `json:"duration"`       //持续时间 单位：s
	DurationTimes  int64 `json:"durationTimes"`  //持续次数
}

var LessonListConfig []*LessonListGroupStru = []*LessonListGroupStru{
	&LessonListGroupStru{
		GroupKey:  "coreData",
		GroupName: "核心数据",
		RuleList: []*LessonRuleConfigStru{
			&LessonRuleConfigStru{
				Key:      "lessonName",
				Function: "GetLessonName",
				Lable:    "章节名称",
				Sort:     10,
			},
			&LessonRuleConfigStru{
				Key:      "lessonStartDate",
				Lable:    "上课时间",
				Function: "GetLessonStartDate",
				Sort:     20,
			},
		},
	},
}
