package middleware

import (
	"deskcrm/api/userprofile"
	"deskcrm/components"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

func SelectBusiness(ctx *gin.Context) {
	userInfoI, exists := ctx.Get("userInfo")
	if !exists {
		authFunc := AuthCheck()
		authFunc(ctx)
		userInfoI, _ = ctx.Get("userInfo")
	}
	userInfo := userInfoI.(*userprofile.UserInfo)
	if userInfo.PreSelectedBusinessUid == 0 {
		if len(userInfo.BusinessUids) != 1 {
			base.RenderJson(ctx, 1004, "未选择业务账号", "")
			ctx.Abort()
			return
		}
		userInfo.SelectedBusinessUid = userInfo.BusinessUids[0]
	} else if !components.Array.InArrayInt64(userInfo.PreSelectedBusinessUid, userInfo.BusinessUids) {
		if len(userInfo.BusinessUids) > 0 {
			base.RenderJson(ctx, 1005, "已解绑业务账号，请重新选择", "")
			ctx.Abort()
			return
		} else {
			base.RenderJson(ctx, 1006, "已解绑业务账号，请重新选择", "")
			ctx.Abort()
			return
		}
	}
	ctx.Set(components.AssistantUid, userInfo.BusinessUids[0])
	ctx.Next()
}
