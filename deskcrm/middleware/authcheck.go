package middleware

import (
	"deskcrm/api/userprofile"
	"deskcrm/components"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
)

const (
	PATH_USERID_DEBUG = "/userprofile/session/debug"
	PATH_AUTH_ACOUNT  = "/assistantdesk/view/new-authority/account"
)

func AuthCheck() gin.HandlerFunc {
	return func(c *gin.Context) {

		userInfoI, exists := c.Get("userInfo")
		userId := 0
		if !exists {
			cookies := c.Request.Cookies()
			if len(cookies) == 0 {
				base.RenderJson(c, 1001, "用户未登录", "")
				c.Abort()
				return
			}

			userID, logErr := userprofile.NewClient().LoginCheck(c)
			if logErr != nil {
				base.RenderJson(c, 1001, "用户未登录！", "")
				c.Abort()
				return
			}

			xuid, _ := c.<PERSON>("XUID")
			if xuid != "" && len(xuid) > 9 {
				xuidInt, err := strconv.Atoi(xuid)
				if err != nil {
					base.RenderJson(c, 1001, "XUID格式错误", "")
					c.Abort()
					return
				}
				debugRole, debugRoleErr := userprofile.NewClient().CheckRouteRole(c, userID, PATH_USERID_DEBUG) // 判断当前登录用户是否有调试权限
				if debugRoleErr != nil {
					base.RenderJson(c, 1001, "鉴权失败:"+debugRoleErr.Error(), "")
					c.Abort()
					return
				}
				accountRole, accountErr := userprofile.NewClient().CheckRouteRole(c, xuidInt, PATH_AUTH_ACOUNT) // XUID 被调试的uid有权限管理编辑的权限不让设置
				if accountErr != nil {
					base.RenderJson(c, 1001, "鉴权失败:"+debugRoleErr.Error(), "")
					c.Abort()
					return
				}
				if !debugRole || accountRole {
					base.RenderJson(c, 1001, "Your are a bad guy", "")
					c.Abort()
					return
				}
				userID = xuidInt
			}

			if userID <= 0 {
				base.RenderJson(c, 1001, "登录过期！", "")
				c.Abort()
				return
			}

			user, err := userprofile.NewClient().GetUserInfo(c, userID)
			if err != nil {
				base.RenderJson(c, 1001, "获取登录用户信息失败", "")
				c.Abort()
				return
			}

			zlog.AddNotice(c, "userprofile user:%+v", user)

			if user == nil || user.UserId == 0 {
				base.RenderJson(c, 1001, "获取登录用户信息失败！", "")
				c.Abort()
				return
			}

			c.Set("userInfo", user)
			userId = user.UserId
			c.Set(components.PersonUid, userID)
		} else {
			userInfo := userInfoI.(*userprofile.UserInfo)
			userId = userInfo.UserId
		}
		if userId <= 0 {
			base.RenderJson(c, 1001, "登录过期！", "")
			c.Abort()
			return
		}
		//check, _ := userprofile.CheckRole(c, userId)
		//if !check {
		//	base.RenderJson(c, 1001, "用户无权限", "")
		//	c.Abort()
		//	return
		//}
		c.Next()
	}
}
